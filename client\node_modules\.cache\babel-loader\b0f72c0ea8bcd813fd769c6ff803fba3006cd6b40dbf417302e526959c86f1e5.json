{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\pages\\\\PatientDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { appointmentAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientDashboard = () => {\n  _s();\n  const [appointments, setAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [cancellingId, setCancellingId] = useState(null);\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n      const response = await appointmentAPI.getMyAppointments();\n      setAppointments(response.data.data.appointments);\n    } catch (error) {\n      setError('Failed to fetch appointments');\n      console.error('Error fetching appointments:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancelAppointment = async appointmentId => {\n    if (!window.confirm('Are you sure you want to cancel this appointment?')) {\n      return;\n    }\n    try {\n      setCancellingId(appointmentId);\n      await appointmentAPI.cancelAppointment(appointmentId, 'Cancelled by patient');\n\n      // Update the appointment in the local state\n      setAppointments(appointments.map(apt => apt._id === appointmentId ? {\n        ...apt,\n        status: 'cancelled'\n      } : apt));\n    } catch (error) {\n      var _error$response, _error$response$data;\n      alert(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to cancel appointment');\n    } finally {\n      setCancellingId(null);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const isUpcoming = (date, timeSlot) => {\n    const appointmentDateTime = new Date(date);\n    const [hours, minutes] = timeSlot.split(':');\n    appointmentDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);\n    return appointmentDateTime > new Date();\n  };\n  const upcomingAppointments = appointments.filter(apt => isUpcoming(apt.date, apt.timeSlot) && ['pending', 'confirmed'].includes(apt.status));\n  const pastAppointments = appointments.filter(apt => !isUpcoming(apt.date, apt.timeSlot) || ['cancelled', 'completed'].includes(apt.status));\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Patient Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-800\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-3 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDCC5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Total Appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: appointments.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\u23F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Upcoming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: upcomingAppointments.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: appointments.filter(apt => apt.status === 'completed').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: [\"Upcoming Appointments (\", upcomingAppointments.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), upcomingAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: upcomingAppointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: [\"Dr. \", appointment.doctor.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 text-sm\",\n                    children: appointment.doctor.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`,\n                  children: appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2\",\n                    children: \"\\uD83D\\uDCC5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 25\n                  }, this), formatDate(appointment.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2\",\n                    children: \"\\u23F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this), appointment.timeSlot]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 23\n                }, this), appointment.reason && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2 mt-0.5\",\n                    children: \"\\uD83D\\uDCDD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: appointment.reason\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), ['pending', 'confirmed'].includes(appointment.status) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCancelAppointment(appointment._id),\n                disabled: cancellingId === appointment._id,\n                className: \"btn-danger text-sm disabled:opacity-50\",\n                children: cancellingId === appointment._id ? 'Cancelling...' : 'Cancel Appointment'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 23\n              }, this)]\n            }, appointment._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-4xl mb-4\",\n              children: \"\\uD83D\\uDCC5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"No upcoming appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/\",\n              className: \"btn-primary mt-4 inline-block\",\n              children: \"Book New Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-4\",\n            children: [\"Past Appointments (\", pastAppointments.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), pastAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 max-h-96 overflow-y-auto\",\n            children: pastAppointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: [\"Dr. \", appointment.doctor.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 text-sm\",\n                    children: appointment.doctor.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`,\n                  children: appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2\",\n                    children: \"\\uD83D\\uDCC5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this), formatDate(appointment.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2\",\n                    children: \"\\u23F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this), appointment.timeSlot]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this), appointment.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-2 mt-0.5\",\n                    children: \"\\uD83D\\uDCCB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: appointment.notes\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this)]\n            }, appointment._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-4xl mb-4\",\n              children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"No past appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientDashboard, \"r0xH4s2VZ5GiFHPusfLwLR6Hzsk=\", false, function () {\n  return [useAuth];\n});\n_c = PatientDashboard;\nexport default PatientDashboard;\nvar _c;\n$RefreshReg$(_c, \"PatientDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "appointmentAPI", "useAuth", "jsxDEV", "_jsxDEV", "PatientDashboard", "_s", "appointments", "setAppointments", "loading", "setLoading", "error", "setError", "cancellingId", "setCancellingId", "user", "fetchAppointments", "response", "getMyAppointments", "data", "console", "handleCancelAppointment", "appointmentId", "window", "confirm", "cancelAppointment", "map", "apt", "_id", "status", "_error$response", "_error$response$data", "alert", "message", "getStatusColor", "formatDate", "dateString", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "isUpcoming", "date", "timeSlot", "appointmentDateTime", "hours", "minutes", "split", "setHours", "parseInt", "upcomingAppointments", "filter", "includes", "pastAppointments", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "length", "appointment", "doctor", "email", "char<PERSON>t", "toUpperCase", "slice", "reason", "onClick", "disabled", "href", "notes", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/pages/PatientDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { appointmentAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\n\nconst PatientDashboard = () => {\n  const [appointments, setAppointments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [cancellingId, setCancellingId] = useState(null);\n  const { user } = useAuth();\n\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n      const response = await appointmentAPI.getMyAppointments();\n      setAppointments(response.data.data.appointments);\n    } catch (error) {\n      setError('Failed to fetch appointments');\n      console.error('Error fetching appointments:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelAppointment = async (appointmentId) => {\n    if (!window.confirm('Are you sure you want to cancel this appointment?')) {\n      return;\n    }\n\n    try {\n      setCancellingId(appointmentId);\n      await appointmentAPI.cancelAppointment(appointmentId, 'Cancelled by patient');\n      \n      // Update the appointment in the local state\n      setAppointments(appointments.map(apt => \n        apt._id === appointmentId \n          ? { ...apt, status: 'cancelled' }\n          : apt\n      ));\n    } catch (error) {\n      alert(error.response?.data?.message || 'Failed to cancel appointment');\n    } finally {\n      setCancellingId(null);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const isUpcoming = (date, timeSlot) => {\n    const appointmentDateTime = new Date(date);\n    const [hours, minutes] = timeSlot.split(':');\n    appointmentDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);\n    return appointmentDateTime > new Date();\n  };\n\n  const upcomingAppointments = appointments.filter(apt => \n    isUpcoming(apt.date, apt.timeSlot) && ['pending', 'confirmed'].includes(apt.status)\n  );\n\n  const pastAppointments = appointments.filter(apt => \n    !isUpcoming(apt.date, apt.timeSlot) || ['cancelled', 'completed'].includes(apt.status)\n  );\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Patient Dashboard</h1>\n          <p className=\"text-gray-600 mt-2\">Welcome back, {user?.name}</p>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-800\">{error}</p>\n          </div>\n        )}\n\n        {/* Quick Stats */}\n        <div className=\"grid md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"card\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">📅</span>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm text-gray-600\">Total Appointments</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{appointments.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">⏰</span>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm text-gray-600\">Upcoming</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{upcomingAppointments.length}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">✅</span>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm text-gray-600\">Completed</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {appointments.filter(apt => apt.status === 'completed').length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-8\">\n          {/* Upcoming Appointments */}\n          <div className=\"card\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n              Upcoming Appointments ({upcomingAppointments.length})\n            </h2>\n            \n            {upcomingAppointments.length > 0 ? (\n              <div className=\"space-y-4\">\n                {upcomingAppointments.map((appointment) => (\n                  <div key={appointment._id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <div>\n                        <h3 className=\"font-semibold text-gray-900\">\n                          Dr. {appointment.doctor.name}\n                        </h3>\n                        <p className=\"text-gray-600 text-sm\">{appointment.doctor.email}</p>\n                      </div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>\n                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"space-y-2 mb-3\">\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <span className=\"mr-2\">📅</span>\n                        {formatDate(appointment.date)}\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <span className=\"mr-2\">⏰</span>\n                        {appointment.timeSlot}\n                      </div>\n                      {appointment.reason && (\n                        <div className=\"flex items-start text-sm text-gray-600\">\n                          <span className=\"mr-2 mt-0.5\">📝</span>\n                          <span>{appointment.reason}</span>\n                        </div>\n                      )}\n                    </div>\n\n                    {['pending', 'confirmed'].includes(appointment.status) && (\n                      <button\n                        onClick={() => handleCancelAppointment(appointment._id)}\n                        disabled={cancellingId === appointment._id}\n                        className=\"btn-danger text-sm disabled:opacity-50\"\n                      >\n                        {cancellingId === appointment._id ? 'Cancelling...' : 'Cancel Appointment'}\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <div className=\"text-gray-400 text-4xl mb-4\">📅</div>\n                <p className=\"text-gray-600\">No upcoming appointments</p>\n                <a href=\"/\" className=\"btn-primary mt-4 inline-block\">\n                  Book New Appointment\n                </a>\n              </div>\n            )}\n          </div>\n\n          {/* Past Appointments */}\n          <div className=\"card\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n              Past Appointments ({pastAppointments.length})\n            </h2>\n            \n            {pastAppointments.length > 0 ? (\n              <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n                {pastAppointments.map((appointment) => (\n                  <div key={appointment._id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-3\">\n                      <div>\n                        <h3 className=\"font-semibold text-gray-900\">\n                          Dr. {appointment.doctor.name}\n                        </h3>\n                        <p className=\"text-gray-600 text-sm\">{appointment.doctor.email}</p>\n                      </div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>\n                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <span className=\"mr-2\">📅</span>\n                        {formatDate(appointment.date)}\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <span className=\"mr-2\">⏰</span>\n                        {appointment.timeSlot}\n                      </div>\n                      {appointment.notes && (\n                        <div className=\"flex items-start text-sm text-gray-600\">\n                          <span className=\"mr-2 mt-0.5\">📋</span>\n                          <span>{appointment.notes}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <div className=\"text-gray-400 text-4xl mb-4\">📋</div>\n                <p className=\"text-gray-600\">No past appointments</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAEgB;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAE1BF,SAAS,CAAC,MAAM;IACdgB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,QAAQ,GAAG,MAAMhB,cAAc,CAACiB,iBAAiB,CAAC,CAAC;MACzDV,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACZ,YAAY,CAAC;IAClD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,QAAQ,CAAC,8BAA8B,CAAC;MACxCQ,OAAO,CAACT,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,uBAAuB,GAAG,MAAOC,aAAa,IAAK;IACvD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MACxE;IACF;IAEA,IAAI;MACFV,eAAe,CAACQ,aAAa,CAAC;MAC9B,MAAMrB,cAAc,CAACwB,iBAAiB,CAACH,aAAa,EAAE,sBAAsB,CAAC;;MAE7E;MACAd,eAAe,CAACD,YAAY,CAACmB,GAAG,CAACC,GAAG,IAClCA,GAAG,CAACC,GAAG,KAAKN,aAAa,GACrB;QAAE,GAAGK,GAAG;QAAEE,MAAM,EAAE;MAAY,CAAC,GAC/BF,GACN,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdC,KAAK,CAAC,EAAAF,eAAA,GAAAnB,KAAK,CAACM,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,8BAA8B,CAAC;IACxE,CAAC,SAAS;MACRnB,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMoB,cAAc,GAAIL,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,WAAW;QACd,OAAO,yBAAyB;MAClC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACrC,MAAMC,mBAAmB,GAAG,IAAIT,IAAI,CAACO,IAAI,CAAC;IAC1C,MAAM,CAACG,KAAK,EAAEC,OAAO,CAAC,GAAGH,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC;IAC5CH,mBAAmB,CAACI,QAAQ,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAEI,QAAQ,CAACH,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,OAAOF,mBAAmB,GAAG,IAAIT,IAAI,CAAC,CAAC;EACzC,CAAC;EAED,MAAMe,oBAAoB,GAAG7C,YAAY,CAAC8C,MAAM,CAAC1B,GAAG,IAClDgB,UAAU,CAAChB,GAAG,CAACiB,IAAI,EAAEjB,GAAG,CAACkB,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACS,QAAQ,CAAC3B,GAAG,CAACE,MAAM,CACpF,CAAC;EAED,MAAM0B,gBAAgB,GAAGhD,YAAY,CAAC8C,MAAM,CAAC1B,GAAG,IAC9C,CAACgB,UAAU,CAAChB,GAAG,CAACiB,IAAI,EAAEjB,GAAG,CAACkB,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACS,QAAQ,CAAC3B,GAAG,CAACE,MAAM,CACvF,CAAC;EAED,IAAIpB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKoD,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvErD,OAAA;QAAKoD,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eAC3CrD,OAAA;MAAKoD,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAErDrD,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrD,OAAA;UAAIoD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEzD,OAAA;UAAGoD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,gBAAc,EAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,EAGLlD,KAAK,iBACJP,OAAA;QAAKoD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClErD,OAAA;UAAGoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAE9C;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACN,eAGDzD,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBrD,OAAA;YAAKoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrD,OAAA;cAAKoD,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChFrD,OAAA;gBAAMoD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBrD,OAAA;gBAAGoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3DzD,OAAA;gBAAGoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAElD,YAAY,CAACwD;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBrD,OAAA;YAAKoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrD,OAAA;cAAKoD,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eACjFrD,OAAA;gBAAMoD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBrD,OAAA;gBAAGoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjDzD,OAAA;gBAAGoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEL,oBAAoB,CAACW;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBrD,OAAA;YAAKoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrD,OAAA;cAAKoD,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFrD,OAAA;gBAAMoD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBrD,OAAA;gBAAGoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDzD,OAAA;gBAAGoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5ClD,YAAY,CAAC8C,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACE,MAAM,KAAK,WAAW,CAAC,CAACkC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExCrD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAIoD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAAC,yBAChC,EAACL,oBAAoB,CAACW,MAAM,EAAC,GACtD;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJT,oBAAoB,CAACW,MAAM,GAAG,CAAC,gBAC9B3D,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBL,oBAAoB,CAAC1B,GAAG,CAAEsC,WAAW,iBACpC5D,OAAA;cAA2BoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAC1ErD,OAAA;gBAAKoD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDrD,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAIoD,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,GAAC,MACtC,EAACO,WAAW,CAACC,MAAM,CAACH,IAAI;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACLzD,OAAA;oBAAGoD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEO,WAAW,CAACC,MAAM,CAACC;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNzD,OAAA;kBAAMoD,SAAS,EAAE,8CAA8CtB,cAAc,CAAC8B,WAAW,CAACnC,MAAM,CAAC,EAAG;kBAAA4B,QAAA,EACjGO,WAAW,CAACnC,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,WAAW,CAACnC,MAAM,CAACwC,KAAK,CAAC,CAAC;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENzD,OAAA;gBAAKoD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrD,OAAA;kBAAKoD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDrD,OAAA;oBAAMoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC/B1B,UAAU,CAAC6B,WAAW,CAACpB,IAAI,CAAC;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDrD,OAAA;oBAAMoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9BG,WAAW,CAACnB,QAAQ;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EACLG,WAAW,CAACM,MAAM,iBACjBlE,OAAA;kBAAKoD,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDrD,OAAA;oBAAMoD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCzD,OAAA;oBAAAqD,QAAA,EAAOO,WAAW,CAACM;kBAAM;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL,CAAC,SAAS,EAAE,WAAW,CAAC,CAACP,QAAQ,CAACU,WAAW,CAACnC,MAAM,CAAC,iBACpDzB,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMlD,uBAAuB,CAAC2C,WAAW,CAACpC,GAAG,CAAE;gBACxD4C,QAAQ,EAAE3D,YAAY,KAAKmD,WAAW,CAACpC,GAAI;gBAC3C4B,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAEjD5C,YAAY,KAAKmD,WAAW,CAACpC,GAAG,GAAG,eAAe,GAAG;cAAoB;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACT;YAAA,GAtCOG,WAAW,CAACpC,GAAG;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuCpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENzD,OAAA;YAAKoD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrD,OAAA;cAAKoD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDzD,OAAA;cAAGoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzDzD,OAAA;cAAGqE,IAAI,EAAC,GAAG;cAACjB,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAIoD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAAC,qBACpC,EAACF,gBAAgB,CAACQ,MAAM,EAAC,GAC9C;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJN,gBAAgB,CAACQ,MAAM,GAAG,CAAC,gBAC1B3D,OAAA;YAAKoD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAChDF,gBAAgB,CAAC7B,GAAG,CAAEsC,WAAW,iBAChC5D,OAAA;cAA2BoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAC1ErD,OAAA;gBAAKoD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDrD,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAIoD,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,GAAC,MACtC,EAACO,WAAW,CAACC,MAAM,CAACH,IAAI;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACLzD,OAAA;oBAAGoD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEO,WAAW,CAACC,MAAM,CAACC;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNzD,OAAA;kBAAMoD,SAAS,EAAE,8CAA8CtB,cAAc,CAAC8B,WAAW,CAACnC,MAAM,CAAC,EAAG;kBAAA4B,QAAA,EACjGO,WAAW,CAACnC,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,WAAW,CAACnC,MAAM,CAACwC,KAAK,CAAC,CAAC;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENzD,OAAA;gBAAKoD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrD,OAAA;kBAAKoD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDrD,OAAA;oBAAMoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC/B1B,UAAU,CAAC6B,WAAW,CAACpB,IAAI,CAAC;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDrD,OAAA;oBAAMoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC9BG,WAAW,CAACnB,QAAQ;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EACLG,WAAW,CAACU,KAAK,iBAChBtE,OAAA;kBAAKoD,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDrD,OAAA;oBAAMoD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCzD,OAAA;oBAAAqD,QAAA,EAAOO,WAAW,CAACU;kBAAK;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA5BEG,WAAW,CAACpC,GAAG;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BpB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENzD,OAAA;YAAKoD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrD,OAAA;cAAKoD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDzD,OAAA;cAAGoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CAzQID,gBAAgB;EAAA,QAKHH,OAAO;AAAA;AAAAyE,EAAA,GALpBtE,gBAAgB;AA2QtB,eAAeA,gBAAgB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}