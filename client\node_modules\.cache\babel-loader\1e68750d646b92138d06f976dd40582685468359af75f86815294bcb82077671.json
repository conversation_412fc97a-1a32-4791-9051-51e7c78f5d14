{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\CommunicationDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiMessageCircle, FiPhone, FiVideo, FiUsers, FiClock, FiX } from 'react-icons/fi';\nimport ChatInterface from './ChatInterface';\nimport VideoCall from './VideoCall';\nimport HelplineSupport from './HelplineSupport';\nimport { chatAPI, callAPI } from '../services/api';\nimport { useSocket } from '../context/SocketContext';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CommunicationDashboard = ({\n  isOpen,\n  onClose,\n  initialReceiver = null\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('conversations');\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [callHistory, setCallHistory] = useState([]);\n  const [showVideoCall, setShowVideoCall] = useState(false);\n  const [currentCall, setCurrentCall] = useState(null);\n  const [showHelpline, setShowHelpline] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    user\n  } = useAuth();\n  const {\n    initiateCall,\n    onlineUsers\n  } = useSocket();\n  useEffect(() => {\n    if (isOpen) {\n      fetchConversations();\n      fetchCallHistory();\n\n      // If initial receiver is provided, start conversation\n      if (initialReceiver) {\n        setSelectedConversation(initialReceiver);\n        setActiveTab('chat');\n      }\n    }\n  }, [isOpen, initialReceiver]);\n  const fetchConversations = async () => {\n    try {\n      setLoading(true);\n      const response = await chatAPI.getConversations();\n      setConversations(response.data.data.conversations);\n    } catch (error) {\n      setError('Failed to load conversations');\n      console.error('Error fetching conversations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCallHistory = async () => {\n    try {\n      const response = await callAPI.getCallHistory({\n        limit: 10\n      });\n      setCallHistory(response.data.data.calls);\n    } catch (error) {\n      console.error('Error fetching call history:', error);\n    }\n  };\n  const handleStartCall = (receiverId, callType, appointmentId = null) => {\n    const callData = {\n      receiverId,\n      callType,\n      appointmentId,\n      roomId: `call_${Date.now()}`,\n      callId: `call_${Date.now()}`\n    };\n    setCurrentCall(callData);\n    setShowVideoCall(true);\n    initiateCall(receiverId, callType, appointmentId);\n  };\n  const handleEndCall = () => {\n    setShowVideoCall(false);\n    setCurrentCall(null);\n  };\n  const isUserOnline = userId => {\n    return onlineUsers.some(user => user.userId === userId);\n  };\n  const formatTime = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getCallStatusColor = status => {\n    switch (status) {\n      case 'answered':\n        return 'text-green-600';\n      case 'missed':\n        return 'text-red-600';\n      case 'rejected':\n        return 'text-orange-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-6xl h-[85vh] flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-80 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Communications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: onClose,\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex border-b border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('conversations'),\n              className: `flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === 'conversations' ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                className: \"inline mr-2\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), \"Chats\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('calls'),\n              className: `flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === 'calls' ? 'border-primary-600 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                className: \"inline mr-2\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), \"Calls\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto\",\n            children: [activeTab === 'conversations' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-2\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center py-8\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this) : conversations.length > 0 ? conversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => {\n                  setSelectedConversation(conversation.participant);\n                  setActiveTab('chat');\n                },\n                className: `p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation._id) === conversation.participant._id ? 'bg-primary-50 border border-primary-200' : 'border border-transparent'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-primary-600 font-semibold\",\n                        children: conversation.participant.name.charAt(0).toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 29\n                    }, this), isUserOnline(conversation.participant._id) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-medium text-gray-900 truncate\",\n                        children: conversation.participant.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatTime(conversation.lastMessage.createdAt)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 truncate\",\n                      children: conversation.lastMessage.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 29\n                    }, this), conversation.unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"inline-block mt-1 px-2 py-1 text-xs bg-primary-600 text-white rounded-full\",\n                      children: conversation.unreadCount\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 25\n                }, this)\n              }, conversation.participant._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                  size: 48,\n                  className: \"mx-auto mb-4 text-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"No conversations yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), activeTab === 'calls' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-2\",\n              children: callHistory.length > 0 ? callHistory.map(call => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border border-gray-200 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded-full ${call.callType === 'video' ? 'bg-blue-100' : 'bg-green-100'}`,\n                    children: call.callType === 'video' ? /*#__PURE__*/_jsxDEV(FiVideo, {\n                      className: \"text-blue-600\",\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(FiPhone, {\n                      className: \"text-green-600\",\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: call.caller._id === user.id ? call.receiver.name : call.caller.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: getCallStatusColor(call.status),\n                        children: call.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: formatTime(call.createdAt)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 31\n                      }, this), call.duration > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"\\u2022\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 239,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [Math.floor(call.duration / 60), \":\", (call.duration % 60).toString().padStart(2, '0')]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 240,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 25\n                }, this)\n              }, call._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 23\n              }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8 text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                  size: 48,\n                  className: \"mx-auto mb-4 text-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"No call history\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowHelpline(true),\n              className: \"w-full btn-secondary text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                className: \"inline mr-2\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), \"Contact Support\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col\",\n          children: activeTab === 'chat' && selectedConversation ? /*#__PURE__*/_jsxDEV(ChatInterface, {\n            receiverId: selectedConversation._id,\n            receiverName: selectedConversation.name,\n            receiverRole: selectedConversation.role,\n            onStartCall: handleStartCall\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex items-center justify-center text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                size: 64,\n                className: \"mx-auto mb-4 text-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: \"Select a conversation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Choose a conversation from the sidebar to start chatting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), showVideoCall && currentCall && /*#__PURE__*/_jsxDEV(VideoCall, {\n      callData: currentCall,\n      onEndCall: handleEndCall,\n      isIncoming: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(HelplineSupport, {\n      isOpen: showHelpline,\n      onClose: () => setShowHelpline(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CommunicationDashboard, \"hlqOpA7RZDhx/mg/b4EMszLLEiw=\", false, function () {\n  return [useAuth, useSocket];\n});\n_c = CommunicationDashboard;\nexport default CommunicationDashboard;\nvar _c;\n$RefreshReg$(_c, \"CommunicationDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiMessageCircle", "FiPhone", "FiVideo", "FiUsers", "<PERSON><PERSON><PERSON>", "FiX", "ChatInterface", "VideoCall", "HelplineSupport", "chatAPI", "callAPI", "useSocket", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CommunicationDashboard", "isOpen", "onClose", "initialReceiver", "_s", "activeTab", "setActiveTab", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "callHistory", "setCallHistory", "showVideoCall", "setShowVideoCall", "currentCall", "setCurrentCall", "showHelpline", "setShowHelpline", "loading", "setLoading", "error", "setError", "user", "initiateCall", "onlineUsers", "fetchConversations", "fetchCallHistory", "response", "getConversations", "data", "console", "getCallHistory", "limit", "calls", "handleStartCall", "receiverId", "callType", "appointmentId", "callData", "roomId", "Date", "now", "callId", "handleEndCall", "isUserOnline", "userId", "some", "formatTime", "timestamp", "toLocaleTimeString", "hour", "minute", "getCallStatusColor", "status", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "length", "map", "conversation", "participant", "_id", "name", "char<PERSON>t", "toUpperCase", "lastMessage", "createdAt", "message", "unreadCount", "call", "caller", "id", "receiver", "duration", "Math", "floor", "toString", "padStart", "<PERSON><PERSON><PERSON>", "receiverR<PERSON>", "role", "onStartCall", "onEndCall", "isIncoming", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/CommunicationDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FiMessageCircle, FiPhone, FiVideo, FiUsers, FiClock, FiX } from 'react-icons/fi';\nimport ChatInterface from './ChatInterface';\nimport VideoCall from './VideoCall';\nimport HelplineSupport from './HelplineSupport';\nimport { chatAPI, callAPI } from '../services/api';\nimport { useSocket } from '../context/SocketContext';\nimport { useAuth } from '../context/AuthContext';\n\nconst CommunicationDashboard = ({ isOpen, onClose, initialReceiver = null }) => {\n  const [activeTab, setActiveTab] = useState('conversations');\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [callHistory, setCallHistory] = useState([]);\n  const [showVideoCall, setShowVideoCall] = useState(false);\n  const [currentCall, setCurrentCall] = useState(null);\n  const [showHelpline, setShowHelpline] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { user } = useAuth();\n  const { initiateCall, onlineUsers } = useSocket();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchConversations();\n      fetchCallHistory();\n      \n      // If initial receiver is provided, start conversation\n      if (initialReceiver) {\n        setSelectedConversation(initialReceiver);\n        setActiveTab('chat');\n      }\n    }\n  }, [isOpen, initialReceiver]);\n\n  const fetchConversations = async () => {\n    try {\n      setLoading(true);\n      const response = await chatAPI.getConversations();\n      setConversations(response.data.data.conversations);\n    } catch (error) {\n      setError('Failed to load conversations');\n      console.error('Error fetching conversations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCallHistory = async () => {\n    try {\n      const response = await callAPI.getCallHistory({ limit: 10 });\n      setCallHistory(response.data.data.calls);\n    } catch (error) {\n      console.error('Error fetching call history:', error);\n    }\n  };\n\n  const handleStartCall = (receiverId, callType, appointmentId = null) => {\n    const callData = {\n      receiverId,\n      callType,\n      appointmentId,\n      roomId: `call_${Date.now()}`,\n      callId: `call_${Date.now()}`\n    };\n    \n    setCurrentCall(callData);\n    setShowVideoCall(true);\n    initiateCall(receiverId, callType, appointmentId);\n  };\n\n  const handleEndCall = () => {\n    setShowVideoCall(false);\n    setCurrentCall(null);\n  };\n\n  const isUserOnline = (userId) => {\n    return onlineUsers.some(user => user.userId === userId);\n  };\n\n  const formatTime = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getCallStatusColor = (status) => {\n    switch (status) {\n      case 'answered':\n        return 'text-green-600';\n      case 'missed':\n        return 'text-red-600';\n      case 'rejected':\n        return 'text-orange-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <>\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 p-4\">\n        <div className=\"bg-white rounded-lg shadow-xl w-full max-w-6xl h-[85vh] flex\">\n          {/* Sidebar */}\n          <div className=\"w-80 border-r border-gray-200 flex flex-col\">\n            {/* Header */}\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Communications</h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors\"\n                >\n                  <FiX size={20} />\n                </button>\n              </div>\n            </div>\n\n            {/* Tabs */}\n            <div className=\"flex border-b border-gray-200\">\n              <button\n                onClick={() => setActiveTab('conversations')}\n                className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === 'conversations'\n                    ? 'border-primary-600 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                <FiMessageCircle className=\"inline mr-2\" size={16} />\n                Chats\n              </button>\n              <button\n                onClick={() => setActiveTab('calls')}\n                className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === 'calls'\n                    ? 'border-primary-600 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                }`}\n              >\n                <FiPhone className=\"inline mr-2\" size={16} />\n                Calls\n              </button>\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 overflow-y-auto\">\n              {activeTab === 'conversations' && (\n                <div className=\"p-4 space-y-2\">\n                  {loading ? (\n                    <div className=\"flex justify-center py-8\">\n                      <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"></div>\n                    </div>\n                  ) : conversations.length > 0 ? (\n                    conversations.map((conversation) => (\n                      <div\n                        key={conversation.participant._id}\n                        onClick={() => {\n                          setSelectedConversation(conversation.participant);\n                          setActiveTab('chat');\n                        }}\n                        className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${\n                          selectedConversation?._id === conversation.participant._id\n                            ? 'bg-primary-50 border border-primary-200'\n                            : 'border border-transparent'\n                        }`}\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"relative\">\n                            <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n                              <span className=\"text-primary-600 font-semibold\">\n                                {conversation.participant.name.charAt(0).toUpperCase()}\n                              </span>\n                            </div>\n                            {isUserOnline(conversation.participant._id) && (\n                              <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n                            )}\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex justify-between items-start\">\n                              <h3 className=\"font-medium text-gray-900 truncate\">\n                                {conversation.participant.name}\n                              </h3>\n                              <span className=\"text-xs text-gray-500\">\n                                {formatTime(conversation.lastMessage.createdAt)}\n                              </span>\n                            </div>\n                            <p className=\"text-sm text-gray-600 truncate\">\n                              {conversation.lastMessage.message}\n                            </p>\n                            {conversation.unreadCount > 0 && (\n                              <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-primary-600 text-white rounded-full\">\n                                {conversation.unreadCount}\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <FiMessageCircle size={48} className=\"mx-auto mb-4 text-gray-300\" />\n                      <p>No conversations yet</p>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {activeTab === 'calls' && (\n                <div className=\"p-4 space-y-2\">\n                  {callHistory.length > 0 ? (\n                    callHistory.map((call) => (\n                      <div key={call._id} className=\"p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className={`p-2 rounded-full ${\n                            call.callType === 'video' ? 'bg-blue-100' : 'bg-green-100'\n                          }`}>\n                            {call.callType === 'video' ? (\n                              <FiVideo className=\"text-blue-600\" size={16} />\n                            ) : (\n                              <FiPhone className=\"text-green-600\" size={16} />\n                            )}\n                          </div>\n                          <div className=\"flex-1\">\n                            <h3 className=\"font-medium text-gray-900\">\n                              {call.caller._id === user.id ? call.receiver.name : call.caller.name}\n                            </h3>\n                            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                              <span className={getCallStatusColor(call.status)}>\n                                {call.status}\n                              </span>\n                              <span>•</span>\n                              <span>{formatTime(call.createdAt)}</span>\n                              {call.duration > 0 && (\n                                <>\n                                  <span>•</span>\n                                  <span>{Math.floor(call.duration / 60)}:{(call.duration % 60).toString().padStart(2, '0')}</span>\n                                </>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <FiClock size={48} className=\"mx-auto mb-4 text-gray-300\" />\n                      <p>No call history</p>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <button\n                onClick={() => setShowHelpline(true)}\n                className=\"w-full btn-secondary text-sm\"\n              >\n                <FiUsers className=\"inline mr-2\" size={16} />\n                Contact Support\n              </button>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1 flex flex-col\">\n            {activeTab === 'chat' && selectedConversation ? (\n              <ChatInterface\n                receiverId={selectedConversation._id}\n                receiverName={selectedConversation.name}\n                receiverRole={selectedConversation.role}\n                onStartCall={handleStartCall}\n              />\n            ) : (\n              <div className=\"flex-1 flex items-center justify-center text-gray-500\">\n                <div className=\"text-center\">\n                  <FiMessageCircle size={64} className=\"mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                    Select a conversation\n                  </h3>\n                  <p>Choose a conversation from the sidebar to start chatting</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Video Call Modal */}\n      {showVideoCall && currentCall && (\n        <VideoCall\n          callData={currentCall}\n          onEndCall={handleEndCall}\n          isIncoming={false}\n        />\n      )}\n\n      {/* Helpline Support Modal */}\n      <HelplineSupport\n        isOpen={showHelpline}\n        onClose={() => setShowHelpline(false)}\n      />\n    </>\n  );\n};\n\nexport default CommunicationDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,QAAQ,gBAAgB;AACzF,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,OAAO,EAAEC,OAAO,QAAQ,iBAAiB;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,eAAe,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,eAAe,CAAC;EAC3D,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAE0C;EAAK,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAE6B,YAAY;IAAEC;EAAY,CAAC,GAAG/B,SAAS,CAAC,CAAC;EAEjDZ,SAAS,CAAC,MAAM;IACd,IAAImB,MAAM,EAAE;MACVyB,kBAAkB,CAAC,CAAC;MACpBC,gBAAgB,CAAC,CAAC;;MAElB;MACA,IAAIxB,eAAe,EAAE;QACnBO,uBAAuB,CAACP,eAAe,CAAC;QACxCG,YAAY,CAAC,MAAM,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACL,MAAM,EAAEE,eAAe,CAAC,CAAC;EAE7B,MAAMuB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMpC,OAAO,CAACqC,gBAAgB,CAAC,CAAC;MACjDrB,gBAAgB,CAACoB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACvB,aAAa,CAAC;IACpD,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,QAAQ,CAAC,8BAA8B,CAAC;MACxCS,OAAO,CAACV,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnC,OAAO,CAACuC,cAAc,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MAC5DrB,cAAc,CAACgB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,KAAK,CAAC;IAC1C,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMc,eAAe,GAAGA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,GAAG,IAAI,KAAK;IACtE,MAAMC,QAAQ,GAAG;MACfH,UAAU;MACVC,QAAQ;MACRC,aAAa;MACbE,MAAM,EAAE,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC5BC,MAAM,EAAE,QAAQF,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED1B,cAAc,CAACuB,QAAQ,CAAC;IACxBzB,gBAAgB,CAAC,IAAI,CAAC;IACtBU,YAAY,CAACY,UAAU,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EACnD,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,gBAAgB,CAAC,KAAK,CAAC;IACvBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM6B,YAAY,GAAIC,MAAM,IAAK;IAC/B,OAAOrB,WAAW,CAACsB,IAAI,CAACxB,IAAI,IAAIA,IAAI,CAACuB,MAAM,KAAKA,MAAM,CAAC;EACzD,CAAC;EAED,MAAME,UAAU,GAAIC,SAAS,IAAK;IAChC,OAAO,IAAIR,IAAI,CAACQ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,IAAI,CAACrD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA;MAAK2D,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7F1D,OAAA;QAAK2D,SAAS,EAAC,8DAA8D;QAAAD,QAAA,gBAE3E1D,OAAA;UAAK2D,SAAS,EAAC,6CAA6C;UAAAD,QAAA,gBAE1D1D,OAAA;YAAK2D,SAAS,EAAC,8BAA8B;YAAAD,QAAA,eAC3C1D,OAAA;cAAK2D,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChD1D,OAAA;gBAAI2D,SAAS,EAAC,qCAAqC;gBAAAD,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvE/D,OAAA;gBACEgE,OAAO,EAAE3D,OAAQ;gBACjBsD,SAAS,EAAC,wFAAwF;gBAAAD,QAAA,eAElG1D,OAAA,CAACT,GAAG;kBAAC0E,IAAI,EAAE;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/D,OAAA;YAAK2D,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC5C1D,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,eAAe,CAAE;cAC7CkD,SAAS,EAAE,qEACTnD,SAAS,KAAK,eAAe,GACzB,qCAAqC,GACrC,sDAAsD,EACzD;cAAAkD,QAAA,gBAEH1D,OAAA,CAACd,eAAe;gBAACyE,SAAS,EAAC,aAAa;gBAACM,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/D,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,OAAO,CAAE;cACrCkD,SAAS,EAAE,qEACTnD,SAAS,KAAK,OAAO,GACjB,qCAAqC,GACrC,sDAAsD,EACzD;cAAAkD,QAAA,gBAEH1D,OAAA,CAACb,OAAO;gBAACwE,SAAS,EAAC,aAAa;gBAACM,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN/D,OAAA;YAAK2D,SAAS,EAAC,wBAAwB;YAAAD,QAAA,GACpClD,SAAS,KAAK,eAAe,iBAC5BR,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAD,QAAA,EAC3BpC,OAAO,gBACNtB,OAAA;gBAAK2D,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,eACvC1D,OAAA;kBAAK2D,SAAS,EAAC;gBAAiE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,GACJrD,aAAa,CAACwD,MAAM,GAAG,CAAC,GAC1BxD,aAAa,CAACyD,GAAG,CAAEC,YAAY,iBAC7BpE,OAAA;gBAEEgE,OAAO,EAAEA,CAAA,KAAM;kBACbnD,uBAAuB,CAACuD,YAAY,CAACC,WAAW,CAAC;kBACjD5D,YAAY,CAAC,MAAM,CAAC;gBACtB,CAAE;gBACFkD,SAAS,EAAE,oEACT,CAAA/C,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE0D,GAAG,MAAKF,YAAY,CAACC,WAAW,CAACC,GAAG,GACtD,yCAAyC,GACzC,2BAA2B,EAC9B;gBAAAZ,QAAA,eAEH1D,OAAA;kBAAK2D,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C1D,OAAA;oBAAK2D,SAAS,EAAC,UAAU;oBAAAD,QAAA,gBACvB1D,OAAA;sBAAK2D,SAAS,EAAC,wEAAwE;sBAAAD,QAAA,eACrF1D,OAAA;wBAAM2D,SAAS,EAAC,gCAAgC;wBAAAD,QAAA,EAC7CU,YAAY,CAACC,WAAW,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;sBAAC;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACLf,YAAY,CAACoB,YAAY,CAACC,WAAW,CAACC,GAAG,CAAC,iBACzCtE,OAAA;sBAAK2D,SAAS,EAAC;oBAAqF;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC3G;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN/D,OAAA;oBAAK2D,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7B1D,OAAA;sBAAK2D,SAAS,EAAC,kCAAkC;sBAAAD,QAAA,gBAC/C1D,OAAA;wBAAI2D,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAC/CU,YAAY,CAACC,WAAW,CAACE;sBAAI;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACL/D,OAAA;wBAAM2D,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,EACpCP,UAAU,CAACiB,YAAY,CAACM,WAAW,CAACC,SAAS;sBAAC;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN/D,OAAA;sBAAG2D,SAAS,EAAC,gCAAgC;sBAAAD,QAAA,EAC1CU,YAAY,CAACM,WAAW,CAACE;oBAAO;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,EACHK,YAAY,CAACS,WAAW,GAAG,CAAC,iBAC3B7E,OAAA;sBAAM2D,SAAS,EAAC,4EAA4E;sBAAAD,QAAA,EACzFU,YAAY,CAACS;oBAAW;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAxCDK,YAAY,CAACC,WAAW,CAACC,GAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyC9B,CACN,CAAC,gBAEF/D,OAAA;gBAAK2D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,gBAC7C1D,OAAA,CAACd,eAAe;kBAAC+E,IAAI,EAAE,EAAG;kBAACN,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpE/D,OAAA;kBAAA0D,QAAA,EAAG;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EAEAvD,SAAS,KAAK,OAAO,iBACpBR,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAD,QAAA,EAC3B5C,WAAW,CAACoD,MAAM,GAAG,CAAC,GACrBpD,WAAW,CAACqD,GAAG,CAAEW,IAAI,iBACnB9E,OAAA;gBAAoB2D,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,eACnE1D,OAAA;kBAAK2D,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C1D,OAAA;oBAAK2D,SAAS,EAAE,oBACdmB,IAAI,CAACtC,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,cAAc,EACzD;oBAAAkB,QAAA,EACAoB,IAAI,CAACtC,QAAQ,KAAK,OAAO,gBACxBxC,OAAA,CAACZ,OAAO;sBAACuE,SAAS,EAAC,eAAe;sBAACM,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE/C/D,OAAA,CAACb,OAAO;sBAACwE,SAAS,EAAC,gBAAgB;sBAACM,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAChD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN/D,OAAA;oBAAK2D,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB1D,OAAA;sBAAI2D,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EACtCoB,IAAI,CAACC,MAAM,CAACT,GAAG,KAAK5C,IAAI,CAACsD,EAAE,GAAGF,IAAI,CAACG,QAAQ,CAACV,IAAI,GAAGO,IAAI,CAACC,MAAM,CAACR;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,eACL/D,OAAA;sBAAK2D,SAAS,EAAC,mDAAmD;sBAAAD,QAAA,gBAChE1D,OAAA;wBAAM2D,SAAS,EAAEH,kBAAkB,CAACsB,IAAI,CAACrB,MAAM,CAAE;wBAAAC,QAAA,EAC9CoB,IAAI,CAACrB;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eACP/D,OAAA;wBAAA0D,QAAA,EAAM;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd/D,OAAA;wBAAA0D,QAAA,EAAOP,UAAU,CAAC2B,IAAI,CAACH,SAAS;sBAAC;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACxCe,IAAI,CAACI,QAAQ,GAAG,CAAC,iBAChBlF,OAAA,CAAAE,SAAA;wBAAAwD,QAAA,gBACE1D,OAAA;0BAAA0D,QAAA,EAAM;wBAAC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACd/D,OAAA;0BAAA0D,QAAA,GAAOyB,IAAI,CAACC,KAAK,CAACN,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACJ,IAAI,CAACI,QAAQ,GAAG,EAAE,EAAEG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;wBAAA;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,eAChG,CACH;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA7BEe,IAAI,CAACR,GAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Bb,CACN,CAAC,gBAEF/D,OAAA;gBAAK2D,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,gBAC7C1D,OAAA,CAACV,OAAO;kBAAC2E,IAAI,EAAE,EAAG;kBAACN,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5D/D,OAAA;kBAAA0D,QAAA,EAAG;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN/D,OAAA;YAAK2D,SAAS,EAAC,8BAA8B;YAAAD,QAAA,eAC3C1D,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,IAAI,CAAE;cACrCsC,SAAS,EAAC,8BAA8B;cAAAD,QAAA,gBAExC1D,OAAA,CAACX,OAAO;gBAACsE,SAAS,EAAC,aAAa;gBAACM,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/D,OAAA;UAAK2D,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAClClD,SAAS,KAAK,MAAM,IAAII,oBAAoB,gBAC3CZ,OAAA,CAACR,aAAa;YACZ+C,UAAU,EAAE3B,oBAAoB,CAAC0D,GAAI;YACrCiB,YAAY,EAAE3E,oBAAoB,CAAC2D,IAAK;YACxCiB,YAAY,EAAE5E,oBAAoB,CAAC6E,IAAK;YACxCC,WAAW,EAAEpD;UAAgB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,gBAEF/D,OAAA;YAAK2D,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACpE1D,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B1D,OAAA,CAACd,eAAe;gBAAC+E,IAAI,EAAE,EAAG;gBAACN,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpE/D,OAAA;gBAAI2D,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAEvD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/D,OAAA;gBAAA0D,QAAA,EAAG;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/C,aAAa,IAAIE,WAAW,iBAC3BlB,OAAA,CAACP,SAAS;MACRiD,QAAQ,EAAExB,WAAY;MACtByE,SAAS,EAAE5C,aAAc;MACzB6C,UAAU,EAAE;IAAM;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF,eAGD/D,OAAA,CAACN,eAAe;MACdU,MAAM,EAAEgB,YAAa;MACrBf,OAAO,EAAEA,CAAA,KAAMgB,eAAe,CAAC,KAAK;IAAE;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACxD,EAAA,CA5SIJ,sBAAsB;EAAA,QAWTL,OAAO,EACcD,SAAS;AAAA;AAAAgG,EAAA,GAZ3C1F,sBAAsB;AA8S5B,eAAeA,sBAAsB;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}