{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  CLEAR_ERROR: 'CLEAR_ERROR'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    loadUser();\n  }, []);\n\n  // Load user from token\n  const loadUser = async () => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: 'No token found'\n      });\n      return;\n    }\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_START\n      });\n      const response = await authAPI.getProfile();\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n        payload: {\n          user: response.data.data.user\n        }\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to load user'\n      });\n    }\n  };\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await authAPI.login(credentials);\n      const {\n        user,\n        token\n      } = response.data.data;\n\n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_START\n      });\n      const response = await authAPI.register(userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n\n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    loadUser,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "REGISTER_START", "REGISTER_SUCCESS", "REGISTER_FAILURE", "LOAD_USER_START", "LOAD_USER_SUCCESS", "LOAD_USER_FAILURE", "CLEAR_ERROR", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "loadUser", "localStorage", "getItem", "response", "getProfile", "data", "_error$response", "_error$response$data", "removeItem", "message", "login", "credentials", "setItem", "JSON", "stringify", "success", "_error$response2", "_error$response2$data", "errorMessage", "register", "userData", "_error$response3", "_error$response3$data", "logout", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["E:/03/client/src/context/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER_START: 'LOAD_USER_START',\n  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',\n  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n    case AUTH_ACTIONS.LOAD_USER_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOAD_USER_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n    case AUTH_ACTIONS.LOAD_USER_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start\n  useEffect(() => {\n    loadUser();\n  }, []);\n\n  // Load user from token\n  const loadUser = async () => {\n    const token = localStorage.getItem('token');\n    \n    if (!token) {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_FAILURE, payload: 'No token found' });\n      return;\n    }\n\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });\n      const response = await authAPI.getProfile();\n      \n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_SUCCESS,\n        payload: { user: response.data.data.user },\n      });\n    } catch (error) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: AUTH_ACTIONS.LOAD_USER_FAILURE,\n        payload: error.response?.data?.message || 'Failed to load user',\n      });\n    }\n  };\n\n  // Login function\n  const login = async (credentials) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n      const response = await authAPI.login(credentials);\n      \n      const { user, token } = response.data.data;\n      \n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, token },\n      });\n      \n      return { success: true, data: response.data };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n      const response = await authAPI.register(userData);\n      \n      const { user, token } = response.data.data;\n      \n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: { user, token },\n      });\n      \n      return { success: true, data: response.data };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    loadUser,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACK,cAAc;IAChC,KAAKL,YAAY,CAACQ,eAAe;MAC/B,OAAO;QACL,GAAGK,KAAK;QACRf,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACE,aAAa;IAC/B,KAAKF,YAAY,CAACM,gBAAgB;MAChC,OAAO;QACL,GAAGO,KAAK;QACRlB,IAAI,EAAEmB,MAAM,CAACE,OAAO,CAACrB,IAAI;QACzBC,KAAK,EAAEkB,MAAM,CAACE,OAAO,CAACpB,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACS,iBAAiB;MACjC,OAAO;QACL,GAAGI,KAAK;QACRlB,IAAI,EAAEmB,MAAM,CAACE,OAAO,CAACrB,IAAI;QACzBE,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACO,gBAAgB;IAClC,KAAKP,YAAY,CAACU,iBAAiB;MACjC,OAAO;QACL,GAAGG,KAAK;QACRlB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEe,MAAM,CAACE;MAChB,CAAC;IAEH,KAAKhB,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGS,KAAK;QACRlB,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,YAAY,CAACW,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRd,KAAK,EAAE;MACT,CAAC;IAEH;MACE,OAAOc,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG9B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM+B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAGhC,UAAU,CAACuB,WAAW,EAAElB,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACdgC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,MAAM1B,KAAK,GAAG2B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAAC5B,KAAK,EAAE;MACVyB,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACU,iBAAiB;QAAEM,OAAO,EAAE;MAAiB,CAAC,CAAC;MAC7E;IACF;IAEA,IAAI;MACFK,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACQ;MAAgB,CAAC,CAAC;MAChD,MAAMiB,QAAQ,GAAG,MAAMlC,OAAO,CAACmC,UAAU,CAAC,CAAC;MAE3CL,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACS,iBAAiB;QACpCO,OAAO,EAAE;UAAErB,IAAI,EAAE8B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAChC;QAAK;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAA6B,eAAA,EAAAC,oBAAA;MACdN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;MAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MAC/BT,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACU,iBAAiB;QACpCM,OAAO,EAAE,EAAAY,eAAA,GAAA7B,KAAK,CAAC0B,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFZ,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACC;MAAY,CAAC,CAAC;MAC5C,MAAMwB,QAAQ,GAAG,MAAMlC,OAAO,CAACyC,KAAK,CAACC,WAAW,CAAC;MAEjD,MAAM;QAAEtC,IAAI;QAAEC;MAAM,CAAC,GAAG6B,QAAQ,CAACE,IAAI,CAACA,IAAI;;MAE1C;MACAJ,YAAY,CAACW,OAAO,CAAC,OAAO,EAAEtC,KAAK,CAAC;MACpC2B,YAAY,CAACW,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACzC,IAAI,CAAC,CAAC;MAElD0B,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACE,aAAa;QAChCc,OAAO,EAAE;UAAErB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEF,OAAO;QAAEyC,OAAO,EAAE,IAAI;QAAEV,IAAI,EAAEF,QAAQ,CAACE;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA,IAAAuC,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAvC,KAAK,CAAC0B,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,cAAc;MACpEV,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACG,aAAa;QAChCa,OAAO,EAAEwB;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEtC,KAAK,EAAEyC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACFrB,QAAQ,CAAC;QAAEN,IAAI,EAAEf,YAAY,CAACK;MAAe,CAAC,CAAC;MAC/C,MAAMoB,QAAQ,GAAG,MAAMlC,OAAO,CAACkD,QAAQ,CAACC,QAAQ,CAAC;MAEjD,MAAM;QAAE/C,IAAI;QAAEC;MAAM,CAAC,GAAG6B,QAAQ,CAACE,IAAI,CAACA,IAAI;;MAE1C;MACAJ,YAAY,CAACW,OAAO,CAAC,OAAO,EAAEtC,KAAK,CAAC;MACpC2B,YAAY,CAACW,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACzC,IAAI,CAAC,CAAC;MAElD0B,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACM,gBAAgB;QACnCU,OAAO,EAAE;UAAErB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEF,OAAO;QAAEyC,OAAO,EAAE,IAAI;QAAEV,IAAI,EAAEF,QAAQ,CAACE;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA,IAAA4C,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,YAAY,GAAG,EAAAG,gBAAA,GAAA5C,KAAK,CAAC0B,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI,qBAAqB;MAC3EV,QAAQ,CAAC;QACPN,IAAI,EAAEf,YAAY,CAACO,gBAAgB;QACnCS,OAAO,EAAEwB;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEtC,KAAK,EAAEyC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnBtB,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BT,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACI;IAAO,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvBzB,QAAQ,CAAC;MAAEN,IAAI,EAAEf,YAAY,CAACW;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMoC,KAAK,GAAG;IACZ,GAAGlC,KAAK;IACRmB,KAAK;IACLS,QAAQ;IACRI,MAAM;IACNvB,QAAQ;IACRwB;EACF,CAAC;EAED,oBACErD,OAAA,CAACwB,WAAW,CAAC+B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5B,QAAA,EAChCA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAhC,EAAA,CAxHaF,YAAY;AAAAmC,EAAA,GAAZnC,YAAY;AAyHzB,OAAO,MAAMoC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGpE,UAAU,CAAC6B,WAAW,CAAC;EACvC,IAAI,CAACuC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAerC,WAAW;AAAC,IAAAoC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}