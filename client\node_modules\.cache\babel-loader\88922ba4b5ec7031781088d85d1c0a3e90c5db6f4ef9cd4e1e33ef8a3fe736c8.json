{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\nconst [a, setA] = useState();\nconst response = await axios.post('http://localhost:5000/api', {\n  name: 'Test Patient',\n  email: '<EMAIL>',\n  password: 'password123',\n  role: 'patient'\n});\nsetAresponse.data;\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API calls\nexport const authAPI = {\n  register: userData => api.post('/auth/register', userData),\n  login: credentials => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me')\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: params => api.get('/doctors', {\n    params\n  }),\n  getDoctorById: id => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: data => api.put('/doctors/profile', data),\n  updateAvailability: availability => api.put('/doctors/availability', {\n    availability\n  })\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) => api.get(`/appointments/available-slots/${doctorId}`, {\n    params: {\n      date\n    }\n  }),\n  bookAppointment: appointmentData => api.post('/appointments/book', appointmentData),\n  getMyAppointments: params => api.get('/appointments/my-appointments', {\n    params\n  }),\n  cancelAppointment: (id, reason) => api.patch(`/appointments/${id}/cancel`, {\n    cancellationReason: reason\n  }),\n  updateAppointmentStatus: (id, status, notes) => api.patch(`/appointments/${id}/status`, {\n    status,\n    notes\n  })\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "a", "setA", "useState", "response", "post", "name", "email", "password", "role", "setAresponse", "data", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "register", "userData", "login", "credentials", "getProfile", "get", "doctor<PERSON><PERSON>", "getAllDoctors", "params", "getDoctorById", "id", "getSpecialties", "getMyProfile", "updateProfile", "put", "updateAvailability", "availability", "appointmentAPI", "getAvailableSlots", "doctorId", "date", "bookAppointment", "appointmentData", "getMyAppointments", "cancelAppointment", "reason", "patch", "cancellationReason", "updateAppointmentStatus", "notes"], "sources": ["E:/03/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\nconst [a, setA] = useState()\n\nconst response = await axios.post('http://localhost:5000/api', {\n  name: 'Test Patient',\n  email: '<EMAIL>',\n  password: 'password123',\n  role: 'patient'\n});\n\nsetAresponse.data\n\n\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API calls\nexport const authAPI = {\n  register: (userData) => api.post('/auth/register', userData),\n  login: (credentials) => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me'),\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: (params) => api.get('/doctors', { params }),\n  getDoctorById: (id) => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: (data) => api.put('/doctors/profile', data),\n  updateAvailability: (availability) => api.put('/doctors/availability', { availability }),\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) => \n    api.get(`/appointments/available-slots/${doctorId}`, { params: { date } }),\n  bookAppointment: (appointmentData) => api.post('/appointments/book', appointmentData),\n  getMyAppointments: (params) => api.get('/appointments/my-appointments', { params }),\n  cancelAppointment: (id, reason) => \n    api.patch(`/appointments/${id}/cancel`, { cancellationReason: reason }),\n  updateAppointmentStatus: (id, status, notes) => \n    api.patch(`/appointments/${id}/status`, { status, notes }),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,MAAM,CAACC,CAAC,EAAEC,IAAI,CAAC,GAAGC,QAAQ,CAAC,CAAC;AAE5B,MAAMC,QAAQ,GAAG,MAAMX,KAAK,CAACY,IAAI,CAAC,2BAA2B,EAAE;EAC7DC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC;AAEFC,YAAY,CAACC,IAAI;;AAIjB;AACAjB,GAAG,CAACkB,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACf,OAAO,CAACmB,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA1B,GAAG,CAACkB,YAAY,CAACR,QAAQ,CAACU,GAAG,CAC1BV,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAgB,KAAK,IAAK;EAAA,IAAAG,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAH,KAAK,CAAChB,QAAQ,cAAAmB,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMS,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAKrC,GAAG,CAACW,IAAI,CAAC,gBAAgB,EAAE0B,QAAQ,CAAC;EAC5DC,KAAK,EAAGC,WAAW,IAAKvC,GAAG,CAACW,IAAI,CAAC,aAAa,EAAE4B,WAAW,CAAC;EAC5DC,UAAU,EAAEA,CAAA,KAAMxC,GAAG,CAACyC,GAAG,CAAC,UAAU;AACtC,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,aAAa,EAAGC,MAAM,IAAK5C,GAAG,CAACyC,GAAG,CAAC,UAAU,EAAE;IAAEG;EAAO,CAAC,CAAC;EAC1DC,aAAa,EAAGC,EAAE,IAAK9C,GAAG,CAACyC,GAAG,CAAC,YAAYK,EAAE,EAAE,CAAC;EAChDC,cAAc,EAAEA,CAAA,KAAM/C,GAAG,CAACyC,GAAG,CAAC,sBAAsB,CAAC;EACrDO,YAAY,EAAEA,CAAA,KAAMhD,GAAG,CAACyC,GAAG,CAAC,qBAAqB,CAAC;EAClDQ,aAAa,EAAGhC,IAAI,IAAKjB,GAAG,CAACkD,GAAG,CAAC,kBAAkB,EAAEjC,IAAI,CAAC;EAC1DkC,kBAAkB,EAAGC,YAAY,IAAKpD,GAAG,CAACkD,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAa,CAAC;AACzF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,iBAAiB,EAAEA,CAACC,QAAQ,EAAEC,IAAI,KAChCxD,GAAG,CAACyC,GAAG,CAAC,iCAAiCc,QAAQ,EAAE,EAAE;IAAEX,MAAM,EAAE;MAAEY;IAAK;EAAE,CAAC,CAAC;EAC5EC,eAAe,EAAGC,eAAe,IAAK1D,GAAG,CAACW,IAAI,CAAC,oBAAoB,EAAE+C,eAAe,CAAC;EACrFC,iBAAiB,EAAGf,MAAM,IAAK5C,GAAG,CAACyC,GAAG,CAAC,+BAA+B,EAAE;IAAEG;EAAO,CAAC,CAAC;EACnFgB,iBAAiB,EAAEA,CAACd,EAAE,EAAEe,MAAM,KAC5B7D,GAAG,CAAC8D,KAAK,CAAC,iBAAiBhB,EAAE,SAAS,EAAE;IAAEiB,kBAAkB,EAAEF;EAAO,CAAC,CAAC;EACzEG,uBAAuB,EAAEA,CAAClB,EAAE,EAAEhB,MAAM,EAAEmC,KAAK,KACzCjE,GAAG,CAAC8D,KAAK,CAAC,iBAAiBhB,EAAE,SAAS,EAAE;IAAEhB,MAAM;IAAEmC;EAAM,CAAC;AAC7D,CAAC;AAED,eAAejE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}