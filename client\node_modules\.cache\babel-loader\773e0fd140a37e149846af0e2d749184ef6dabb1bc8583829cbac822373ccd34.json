{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg border-b border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white font-bold text-lg\",\n              children: \"D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"DocBook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n            children: \"Find Doctors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: (user === null || user === void 0 ? void 0 : user.role) === 'doctor' ? '/doctor-dashboard' : '/patient-dashboard',\n              className: \"text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"btn-secondary\",\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"btn-primary\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-700 hover:text-primary-600\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "name", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/Navbar.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst Navbar = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link to=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">D</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">DocBook</span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link \n              to=\"/\" \n              className=\"text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              Find Doctors\n            </Link>\n            \n            {isAuthenticated ? (\n              <>\n                {/* Authenticated User Links */}\n                <Link \n                  to={user?.role === 'doctor' ? '/doctor-dashboard' : '/patient-dashboard'}\n                  className=\"text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n                >\n                  Dashboard\n                </Link>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <span className=\"text-gray-700\">\n                    Welcome, {user?.name}\n                  </span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"btn-secondary\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </>\n            ) : (\n              <>\n                {/* Guest Links */}\n                <Link \n                  to=\"/login\" \n                  className=\"text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n                >\n                  Login\n                </Link>\n                <Link \n                  to=\"/register\" \n                  className=\"btn-primary\"\n                >\n                  Sign Up\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button className=\"text-gray-700 hover:text-primary-600\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBF,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACER,OAAA;IAAKU,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC1DX,OAAA;MAAKU,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDX,OAAA;QAAKU,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDX,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAClDX,OAAA;YAAKU,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjFX,OAAA;cAAMU,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNhB,OAAA;YAAMU,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAGPhB,OAAA;UAAKU,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDX,OAAA,CAACJ,IAAI;YACHgB,EAAE,EAAC,GAAG;YACNF,SAAS,EAAC,qEAAqE;YAAAC,QAAA,EAChF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAENV,eAAe,gBACdN,OAAA,CAAAE,SAAA;YAAAS,QAAA,gBAEEX,OAAA,CAACJ,IAAI;cACHgB,EAAE,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI,MAAK,QAAQ,GAAG,mBAAmB,GAAG,oBAAqB;cACzEP,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAChF;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPhB,OAAA;cAAKU,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CX,OAAA;gBAAMU,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WACrB,EAACN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACPhB,OAAA;gBACEmB,OAAO,EAAEV,YAAa;gBACtBC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC1B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,eACN,CAAC,gBAEHhB,OAAA,CAAAE,SAAA;YAAAS,QAAA,gBAEEX,OAAA,CAACJ,IAAI;cACHgB,EAAE,EAAC,QAAQ;cACXF,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAChF;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhB,OAAA,CAACJ,IAAI;cACHgB,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,aAAa;cAAAC,QAAA,EACxB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNhB,OAAA;UAAKU,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBX,OAAA;YAAQU,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACtDX,OAAA;cAAKU,SAAS,EAAC,SAAS;cAACU,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAX,QAAA,eAC5EX,OAAA;gBAAMuB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CAnFID,MAAM;EAAA,QACgCL,OAAO,EAChCD,WAAW;AAAA;AAAA8B,EAAA,GAFxBxB,MAAM;AAqFZ,eAAeA,MAAM;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}