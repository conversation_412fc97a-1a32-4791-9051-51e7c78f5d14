{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API calls\nexport const authAPI = {\n  register: userData => api.post('/auth/register', userData),\n  login: credentials => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me')\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: params => api.get('/doctors', {\n    params\n  }),\n  getDoctorById: id => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: data => api.put('/doctors/profile', data),\n  updateAvailability: availability => api.put('/doctors/availability', {\n    availability\n  })\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) => api.get(`/appointments/available-slots/${doctorId}`, {\n    params: {\n      date\n    }\n  }),\n  bookAppointment: appointmentData => api.post('/appointments/book', appointmentData),\n  getMyAppointments: params => api.get('/appointments/my-appointments', {\n    params\n  }),\n  cancelAppointment: (id, reason) => api.patch(`/appointments/${id}/cancel`, {\n    cancellationReason: reason\n  }),\n  updateAppointmentStatus: (id, status, notes) => api.patch(`/appointments/${id}/status`, {\n    status,\n    notes\n  })\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "register", "userData", "post", "login", "credentials", "getProfile", "get", "doctor<PERSON><PERSON>", "getAllDoctors", "params", "getDoctorById", "id", "getSpecialties", "getMyProfile", "updateProfile", "data", "put", "updateAvailability", "availability", "appointmentAPI", "getAvailableSlots", "doctorId", "date", "bookAppointment", "appointmentData", "getMyAppointments", "cancelAppointment", "reason", "patch", "cancellationReason", "updateAppointmentStatus", "notes"], "sources": ["E:/03/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API calls\nexport const authAPI = {\n  register: (userData) => api.post('/auth/register', userData),\n  login: (credentials) => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me'),\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: (params) => api.get('/doctors', { params }),\n  getDoctorById: (id) => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: (data) => api.put('/doctors/profile', data),\n  updateAvailability: (availability) => api.put('/doctors/availability', { availability }),\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) => \n    api.get(`/appointments/available-slots/${doctorId}`, { params: { date } }),\n  bookAppointment: (appointmentData) => api.post('/appointments/book', appointmentData),\n  getMyAppointments: (params) => api.get('/appointments/my-appointments', { params }),\n  cancelAppointment: (id, reason) => \n    api.patch(`/appointments/${id}/cancel`, { cancellationReason: reason }),\n  updateAppointmentStatus: (id, status, notes) => \n    api.patch(`/appointments/${id}/status`, { status, notes }),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAN,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAK3B,GAAG,CAAC4B,IAAI,CAAC,gBAAgB,EAAED,QAAQ,CAAC;EAC5DE,KAAK,EAAGC,WAAW,IAAK9B,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAEE,WAAW,CAAC;EAC5DC,UAAU,EAAEA,CAAA,KAAM/B,GAAG,CAACgC,GAAG,CAAC,UAAU;AACtC,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,aAAa,EAAGC,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,UAAU,EAAE;IAAEG;EAAO,CAAC,CAAC;EAC1DC,aAAa,EAAGC,EAAE,IAAKrC,GAAG,CAACgC,GAAG,CAAC,YAAYK,EAAE,EAAE,CAAC;EAChDC,cAAc,EAAEA,CAAA,KAAMtC,GAAG,CAACgC,GAAG,CAAC,sBAAsB,CAAC;EACrDO,YAAY,EAAEA,CAAA,KAAMvC,GAAG,CAACgC,GAAG,CAAC,qBAAqB,CAAC;EAClDQ,aAAa,EAAGC,IAAI,IAAKzC,GAAG,CAAC0C,GAAG,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAC1DE,kBAAkB,EAAGC,YAAY,IAAK5C,GAAG,CAAC0C,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAa,CAAC;AACzF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,iBAAiB,EAAEA,CAACC,QAAQ,EAAEC,IAAI,KAChChD,GAAG,CAACgC,GAAG,CAAC,iCAAiCe,QAAQ,EAAE,EAAE;IAAEZ,MAAM,EAAE;MAAEa;IAAK;EAAE,CAAC,CAAC;EAC5EC,eAAe,EAAGC,eAAe,IAAKlD,GAAG,CAAC4B,IAAI,CAAC,oBAAoB,EAAEsB,eAAe,CAAC;EACrFC,iBAAiB,EAAGhB,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,+BAA+B,EAAE;IAAEG;EAAO,CAAC,CAAC;EACnFiB,iBAAiB,EAAEA,CAACf,EAAE,EAAEgB,MAAM,KAC5BrD,GAAG,CAACsD,KAAK,CAAC,iBAAiBjB,EAAE,SAAS,EAAE;IAAEkB,kBAAkB,EAAEF;EAAO,CAAC,CAAC;EACzEG,uBAAuB,EAAEA,CAACnB,EAAE,EAAEjB,MAAM,EAAEqC,KAAK,KACzCzD,GAAG,CAACsD,KAAK,CAAC,iBAAiBjB,EAAE,SAAS,EAAE;IAAEjB,MAAM;IAAEqC;EAAM,CAAC;AAC7D,CAAC;AAED,eAAezD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}