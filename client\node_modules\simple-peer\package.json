{"name": "simple-peer", "description": "Simple one-to-one WebRTC video/voice and data channels", "version": "9.11.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/simple-peer/issues"}, "dependencies": {"buffer": "^6.0.3", "debug": "^4.3.2", "err-code": "^3.0.1", "get-browser-rtc": "^1.1.0", "queue-microtask": "^1.2.3", "randombytes": "^2.1.0", "readable-stream": "^3.6.0"}, "devDependencies": {"airtap": "^4.0.3", "airtap-manual": "^1.0.0", "airtap-sauce": "^1.1.0", "babel-minify": "^0.5.1", "bowser": "^2.11.0", "browserify": "^17.0.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "prettier-bytes": "^1.0.4", "simple-get": "^4.0.0", "speedometer": "^1.1.0", "standard": "*", "string-to-stream": "^3.0.1", "tape": "^5.5.2", "thunky": "^1.1.0", "wrtc": "^0.4.7", "ws": "^7.5.3"}, "keywords": ["data", "data channel", "data channel stream", "data channels", "p2p", "peer", "peer", "peer-to-peer", "stream", "video", "voice", "webrtc", "webrtc stream"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/simple-peer.git"}, "scripts": {"build": "browserify -s SimplePeer -r . | minify > simplepeer.min.js", "size": "npm run build && cat simplepeer.min.js | gzip | wc -c", "// test": "standard && npm run test-node && npm run test-browser", "test": "standard && npm run test-browser", "test-browser": "airtap --coverage --concurrency 1 -- test/*.js", "test-browser-local": "airtap --coverage --preset local -- test/*.js", "test-node": "WRTC=wrtc tape test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}