/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { Config } from '@jest/types';
export declare function clearCachedLookups(): void;
export default function cachedShouldLoadAsEsm(path: Config.Path, extensionsToTreatAsEsm: Array<Config.Path>): boolean;
