{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\CommunicationTest.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSocket } from '../context/SocketContext';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CommunicationTest = () => {\n  _s();\n  const [testMessage, setTestMessage] = useState('');\n  const [testResults, setTestResults] = useState([]);\n  const {\n    socket,\n    isConnected,\n    sendMessage,\n    joinChat,\n    initiateCall,\n    sendHelplineMessage,\n    notifications\n  } = useSocket();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    if (isConnected) {\n      addTestResult('✅ Socket.io connected successfully');\n    } else {\n      addTestResult('❌ Socket.io connection failed');\n    }\n  }, [isConnected]);\n  const addTestResult = message => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n  const testSocketConnection = () => {\n    if (socket) {\n      addTestResult('✅ Socket object exists');\n      addTestResult(`✅ Connected: ${isConnected}`);\n      addTestResult(`✅ User authenticated: ${isAuthenticated}`);\n      addTestResult(`✅ User ID: ${user === null || user === void 0 ? void 0 : user._id}`);\n    } else {\n      addTestResult('❌ Socket object not found');\n    }\n  };\n  const testChat = () => {\n    if (!isAuthenticated) {\n      addTestResult('❌ User not authenticated for chat test');\n      return;\n    }\n\n    // Test with a doctor ID (using first doctor from seed data)\n    const testReceiverId = '507f1f77bcf86cd799439011'; // Mock doctor ID\n    joinChat(testReceiverId);\n    sendMessage(testReceiverId, 'Test message from communication test');\n    addTestResult('✅ Chat test message sent');\n  };\n  const testVideoCall = () => {\n    if (!isAuthenticated) {\n      addTestResult('❌ User not authenticated for video call test');\n      return;\n    }\n    const testReceiverId = '507f1f77bcf86cd799439011'; // Mock doctor ID\n    initiateCall(testReceiverId, 'video');\n    addTestResult('✅ Video call test initiated');\n  };\n  const testHelpline = () => {\n    sendHelplineMessage('Test helpline message');\n    addTestResult('✅ Helpline test message sent');\n  };\n  const clearResults = () => {\n    setTestResults([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold mb-6\",\n      children: \"Communication System Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-4 border rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: \"Connection Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: isConnected ? 'Connected' : 'Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: isAuthenticated ? `Logged in as: ${user === null || user === void 0 ? void 0 : user.name}` : 'Not authenticated'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: \"Test Functions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testSocketConnection,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n          children: \"Test Socket Connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testChat,\n          className: \"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\",\n          disabled: !isAuthenticated,\n          children: \"Test Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testVideoCall,\n          className: \"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\",\n          disabled: !isAuthenticated,\n          children: \"Test Video Call\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testHelpline,\n          className: \"px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700\",\n          children: \"Test Helpline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearResults,\n          className: \"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\",\n          children: \"Clear Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-4 border rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: \"Custom Message Test\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: testMessage,\n          onChange: e => setTestMessage(e.target.value),\n          placeholder: \"Enter test message\",\n          className: \"flex-1 px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            if (testMessage.trim()) {\n              sendHelplineMessage(testMessage);\n              addTestResult(`✅ Custom message sent: ${testMessage}`);\n              setTestMessage('');\n            }\n          },\n          className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n          children: \"Send\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-4 border rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: [\"Recent Notifications (\", notifications.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-h-32 overflow-y-auto\",\n        children: notifications.length > 0 ? notifications.map((notification, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm p-2 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [notification.title, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 17\n          }, this), \" \", notification.message]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: \"Test Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-h-64 overflow-y-auto bg-gray-50 p-3 rounded\",\n        children: testResults.length > 0 ? testResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-mono mb-1\",\n          children: result\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No test results yet. Click a test button to start.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 p-4 border rounded-lg bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-2\",\n        children: \"Debug Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Socket Connected:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 16\n          }, this), \" \", isConnected ? 'Yes' : 'No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"User Authenticated:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 16\n          }, this), \" \", isAuthenticated ? 'Yes' : 'No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"User ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 16\n          }, this), \" \", (user === null || user === void 0 ? void 0 : user._id) || 'Not available']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"User Role:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 16\n          }, this), \" \", (user === null || user === void 0 ? void 0 : user.role) || 'Not available']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Socket Object:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 16\n          }, this), \" \", socket ? 'Available' : 'Not available']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Notifications Count:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 16\n          }, this), \" \", notifications.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(CommunicationTest, \"BmyN2Xr8WJDNcQDa8mKTJGWqRqY=\", false, function () {\n  return [useSocket, useAuth];\n});\n_c = CommunicationTest;\nexport default CommunicationTest;\nvar _c;\n$RefreshReg$(_c, \"CommunicationTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSocket", "useAuth", "jsxDEV", "_jsxDEV", "CommunicationTest", "_s", "testMessage", "setTestMessage", "testResults", "setTestResults", "socket", "isConnected", "sendMessage", "joinChat", "initiateCall", "sendHelplineMessage", "notifications", "user", "isAuthenticated", "addTestResult", "message", "prev", "Date", "toLocaleTimeString", "testSocketConnection", "_id", "testChat", "testReceiverId", "testVideoCall", "testHelpline", "clearResults", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "disabled", "type", "value", "onChange", "e", "target", "placeholder", "trim", "length", "map", "notification", "index", "title", "result", "role", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/CommunicationTest.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSocket } from '../context/SocketContext';\nimport { useAuth } from '../context/AuthContext';\n\nconst CommunicationTest = () => {\n  const [testMessage, setTestMessage] = useState('');\n  const [testResults, setTestResults] = useState([]);\n  \n  const { \n    socket, \n    isConnected, \n    sendMessage, \n    joinChat,\n    initiateCall,\n    sendHelplineMessage,\n    notifications \n  } = useSocket();\n  \n  const { user, isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (isConnected) {\n      addTestResult('✅ Socket.io connected successfully');\n    } else {\n      addTestResult('❌ Socket.io connection failed');\n    }\n  }, [isConnected]);\n\n  const addTestResult = (message) => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n\n  const testSocketConnection = () => {\n    if (socket) {\n      addTestResult('✅ Socket object exists');\n      addTestResult(`✅ Connected: ${isConnected}`);\n      addTestResult(`✅ User authenticated: ${isAuthenticated}`);\n      addTestResult(`✅ User ID: ${user?._id}`);\n    } else {\n      addTestResult('❌ Socket object not found');\n    }\n  };\n\n  const testChat = () => {\n    if (!isAuthenticated) {\n      addTestResult('❌ User not authenticated for chat test');\n      return;\n    }\n    \n    // Test with a doctor ID (using first doctor from seed data)\n    const testReceiverId = '507f1f77bcf86cd799439011'; // Mock doctor ID\n    joinChat(testReceiverId);\n    sendMessage(testReceiverId, 'Test message from communication test');\n    addTestResult('✅ Chat test message sent');\n  };\n\n  const testVideoCall = () => {\n    if (!isAuthenticated) {\n      addTestResult('❌ User not authenticated for video call test');\n      return;\n    }\n    \n    const testReceiverId = '507f1f77bcf86cd799439011'; // Mock doctor ID\n    initiateCall(testReceiverId, 'video');\n    addTestResult('✅ Video call test initiated');\n  };\n\n  const testHelpline = () => {\n    sendHelplineMessage('Test helpline message');\n    addTestResult('✅ Helpline test message sent');\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <h2 className=\"text-2xl font-bold mb-6\">Communication System Test</h2>\n      \n      {/* Connection Status */}\n      <div className=\"mb-6 p-4 border rounded-lg\">\n        <h3 className=\"text-lg font-semibold mb-2\">Connection Status</h3>\n        <div className=\"flex items-center space-x-4\">\n          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>\n          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>\n          <span className=\"text-sm text-gray-600\">\n            {isAuthenticated ? `Logged in as: ${user?.name}` : 'Not authenticated'}\n          </span>\n        </div>\n      </div>\n\n      {/* Test Buttons */}\n      <div className=\"mb-6 space-y-2\">\n        <h3 className=\"text-lg font-semibold mb-2\">Test Functions</h3>\n        <div className=\"flex flex-wrap gap-2\">\n          <button\n            onClick={testSocketConnection}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            Test Socket Connection\n          </button>\n          <button\n            onClick={testChat}\n            className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n            disabled={!isAuthenticated}\n          >\n            Test Chat\n          </button>\n          <button\n            onClick={testVideoCall}\n            className=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\"\n            disabled={!isAuthenticated}\n          >\n            Test Video Call\n          </button>\n          <button\n            onClick={testHelpline}\n            className=\"px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700\"\n          >\n            Test Helpline\n          </button>\n          <button\n            onClick={clearResults}\n            className=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\"\n          >\n            Clear Results\n          </button>\n        </div>\n      </div>\n\n      {/* Custom Message Test */}\n      <div className=\"mb-6 p-4 border rounded-lg\">\n        <h3 className=\"text-lg font-semibold mb-2\">Custom Message Test</h3>\n        <div className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={testMessage}\n            onChange={(e) => setTestMessage(e.target.value)}\n            placeholder=\"Enter test message\"\n            className=\"flex-1 px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n          <button\n            onClick={() => {\n              if (testMessage.trim()) {\n                sendHelplineMessage(testMessage);\n                addTestResult(`✅ Custom message sent: ${testMessage}`);\n                setTestMessage('');\n              }\n            }}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            Send\n          </button>\n        </div>\n      </div>\n\n      {/* Notifications */}\n      <div className=\"mb-6 p-4 border rounded-lg\">\n        <h3 className=\"text-lg font-semibold mb-2\">Recent Notifications ({notifications.length})</h3>\n        <div className=\"max-h-32 overflow-y-auto\">\n          {notifications.length > 0 ? (\n            notifications.map((notification, index) => (\n              <div key={index} className=\"text-sm p-2 border-b\">\n                <strong>{notification.title}:</strong> {notification.message}\n              </div>\n            ))\n          ) : (\n            <p className=\"text-gray-500\">No notifications</p>\n          )}\n        </div>\n      </div>\n\n      {/* Test Results */}\n      <div className=\"p-4 border rounded-lg\">\n        <h3 className=\"text-lg font-semibold mb-2\">Test Results</h3>\n        <div className=\"max-h-64 overflow-y-auto bg-gray-50 p-3 rounded\">\n          {testResults.length > 0 ? (\n            testResults.map((result, index) => (\n              <div key={index} className=\"text-sm font-mono mb-1\">\n                {result}\n              </div>\n            ))\n          ) : (\n            <p className=\"text-gray-500\">No test results yet. Click a test button to start.</p>\n          )}\n        </div>\n      </div>\n\n      {/* Debug Info */}\n      <div className=\"mt-6 p-4 border rounded-lg bg-gray-50\">\n        <h3 className=\"text-lg font-semibold mb-2\">Debug Information</h3>\n        <div className=\"text-sm space-y-1\">\n          <div><strong>Socket Connected:</strong> {isConnected ? 'Yes' : 'No'}</div>\n          <div><strong>User Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</div>\n          <div><strong>User ID:</strong> {user?._id || 'Not available'}</div>\n          <div><strong>User Role:</strong> {user?.role || 'Not available'}</div>\n          <div><strong>Socket Object:</strong> {socket ? 'Available' : 'Not available'}</div>\n          <div><strong>Notifications Count:</strong> {notifications.length}</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CommunicationTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM;IACJY,MAAM;IACNC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,YAAY;IACZC,mBAAmB;IACnBC;EACF,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAEf,MAAM;IAAEiB,IAAI;IAAEC;EAAgB,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAE3CF,SAAS,CAAC,MAAM;IACd,IAAIY,WAAW,EAAE;MACfQ,aAAa,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACLA,aAAa,CAAC,+BAA+B,CAAC;IAChD;EACF,CAAC,EAAE,CAACR,WAAW,CAAC,CAAC;EAEjB,MAAMQ,aAAa,GAAIC,OAAO,IAAK;IACjCX,cAAc,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,KAAKH,OAAO,EAAE,CAAC,CAAC;EACrF,CAAC;EAED,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAId,MAAM,EAAE;MACVS,aAAa,CAAC,wBAAwB,CAAC;MACvCA,aAAa,CAAC,gBAAgBR,WAAW,EAAE,CAAC;MAC5CQ,aAAa,CAAC,yBAAyBD,eAAe,EAAE,CAAC;MACzDC,aAAa,CAAC,cAAcF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,GAAG,EAAE,CAAC;IAC1C,CAAC,MAAM;MACLN,aAAa,CAAC,2BAA2B,CAAC;IAC5C;EACF,CAAC;EAED,MAAMO,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI,CAACR,eAAe,EAAE;MACpBC,aAAa,CAAC,wCAAwC,CAAC;MACvD;IACF;;IAEA;IACA,MAAMQ,cAAc,GAAG,0BAA0B,CAAC,CAAC;IACnDd,QAAQ,CAACc,cAAc,CAAC;IACxBf,WAAW,CAACe,cAAc,EAAE,sCAAsC,CAAC;IACnER,aAAa,CAAC,0BAA0B,CAAC;EAC3C,CAAC;EAED,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACV,eAAe,EAAE;MACpBC,aAAa,CAAC,8CAA8C,CAAC;MAC7D;IACF;IAEA,MAAMQ,cAAc,GAAG,0BAA0B,CAAC,CAAC;IACnDb,YAAY,CAACa,cAAc,EAAE,OAAO,CAAC;IACrCR,aAAa,CAAC,6BAA6B,CAAC;EAC9C,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBd,mBAAmB,CAAC,uBAAuB,CAAC;IAC5CI,aAAa,CAAC,8BAA8B,CAAC;EAC/C,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzBrB,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,oBACEN,OAAA;IAAK4B,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAClE7B,OAAA;MAAI4B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGtEjC,OAAA;MAAK4B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEjC,OAAA;QAAK4B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C7B,OAAA;UAAK4B,SAAS,EAAE,wBAAwBpB,WAAW,GAAG,cAAc,GAAG,YAAY;QAAG;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7FjC,OAAA;UAAA6B,QAAA,EAAOrB,WAAW,GAAG,WAAW,GAAG;QAAc;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzDjC,OAAA;UAAM4B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACpCd,eAAe,GAAG,iBAAiBD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,IAAI,EAAE,GAAG;QAAmB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9DjC,OAAA;QAAK4B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC7B,OAAA;UACEmC,OAAO,EAAEd,oBAAqB;UAC9BO,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EACvE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA;UACEmC,OAAO,EAAEZ,QAAS;UAClBK,SAAS,EAAC,8DAA8D;UACxEQ,QAAQ,EAAE,CAACrB,eAAgB;UAAAc,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA;UACEmC,OAAO,EAAEV,aAAc;UACvBG,SAAS,EAAC,gEAAgE;UAC1EQ,QAAQ,EAAE,CAACrB,eAAgB;UAAAc,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA;UACEmC,OAAO,EAAET,YAAa;UACtBE,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAC3E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA;UACEmC,OAAO,EAAER,YAAa;UACtBC,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EACvE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEjC,OAAA;QAAK4B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7B,OAAA;UACEqC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEnC,WAAY;UACnBoC,QAAQ,EAAGC,CAAC,IAAKpC,cAAc,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDI,WAAW,EAAC,oBAAoB;UAChCd,SAAS,EAAC;QAAqF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACFjC,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIhC,WAAW,CAACwC,IAAI,CAAC,CAAC,EAAE;cACtB/B,mBAAmB,CAACT,WAAW,CAAC;cAChCa,aAAa,CAAC,0BAA0Bb,WAAW,EAAE,CAAC;cACtDC,cAAc,CAAC,EAAE,CAAC;YACpB;UACF,CAAE;UACFwB,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EACvE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,wBAAsB,EAAChB,aAAa,CAAC+B,MAAM,EAAC,GAAC;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7FjC,OAAA;QAAK4B,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EACtChB,aAAa,CAAC+B,MAAM,GAAG,CAAC,GACvB/B,aAAa,CAACgC,GAAG,CAAC,CAACC,YAAY,EAAEC,KAAK,kBACpC/C,OAAA;UAAiB4B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAC/C7B,OAAA;YAAA6B,QAAA,GAASiB,YAAY,CAACE,KAAK,EAAC,GAAC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACa,YAAY,CAAC7B,OAAO;QAAA,GADpD8B,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN,CAAC,gBAEFjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MACjD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpC7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5DjC,OAAA;QAAK4B,SAAS,EAAC,iDAAiD;QAAAC,QAAA,EAC7DxB,WAAW,CAACuC,MAAM,GAAG,CAAC,GACrBvC,WAAW,CAACwC,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK,kBAC5B/C,OAAA;UAAiB4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAChDoB;QAAM,GADCF,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN,CAAC,gBAEFjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MACnF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD7B,OAAA;QAAI4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEjC,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7B,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzB,WAAW,GAAG,KAAK,GAAG,IAAI;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1EjC,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAClB,eAAe,GAAG,KAAK,GAAG,IAAI;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChFjC,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,GAAG,KAAI,eAAe;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnEjC,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,KAAI,eAAe;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtEjC,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC1B,MAAM,GAAG,WAAW,GAAG,eAAe;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFjC,OAAA;UAAA6B,QAAA,gBAAK7B,OAAA;YAAA6B,QAAA,EAAQ;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpB,aAAa,CAAC+B,MAAM;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvMID,iBAAiB;EAAA,QAYjBJ,SAAS,EAEqBC,OAAO;AAAA;AAAAqD,EAAA,GAdrClD,iBAAiB;AAyMvB,eAAeA,iBAAiB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}