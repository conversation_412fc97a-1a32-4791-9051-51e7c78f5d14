{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\VideoCall.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiVideo, FiVideoOff, FiMic, FiMicOff, FiPhone, FiPhoneOff, FiMaximize2, FiMinimize2 } from 'react-icons/fi';\nimport useWebRTC from '../hooks/useWebRTC';\nimport { useSocket } from '../context/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  callData,\n  onEndCall,\n  isIncoming = false\n}) => {\n  _s();\n  var _callData$caller, _callData$receiver, _callData$caller2, _callData$caller2$nam, _callData$receiver2, _callData$receiver2$n;\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [callDuration, setCallDuration] = useState(0);\n  const [callStartTime, setCallStartTime] = useState(null);\n  const {\n    endCall: socketEndCall\n  } = useSocket();\n  const {\n    localStream,\n    remoteStream,\n    isCallActive,\n    isVideoEnabled,\n    isAudioEnabled,\n    callError,\n    connectionState,\n    localVideoRef,\n    remoteVideoRef,\n    startCall,\n    answerCall,\n    endCall,\n    toggleVideo,\n    toggleAudio\n  } = useWebRTC(callData === null || callData === void 0 ? void 0 : callData.roomId, !isIncoming);\n\n  // Timer for call duration\n  useEffect(() => {\n    let interval;\n    if (isCallActive && callStartTime) {\n      interval = setInterval(() => {\n        setCallDuration(Math.floor((Date.now() - callStartTime) / 1000));\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [isCallActive, callStartTime]);\n\n  // Start call when component mounts (for outgoing calls)\n  useEffect(() => {\n    if (!isIncoming && callData) {\n      startCall(callData.callType === 'video', true);\n      setCallStartTime(Date.now());\n    }\n  }, [isIncoming, callData, startCall]);\n\n  // Handle incoming call answer\n  const handleAnswerCall = () => {\n    answerCall(callData.callType === 'video', true);\n    setCallStartTime(Date.now());\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    socketEndCall(callData === null || callData === void 0 ? void 0 : callData.callId, callData === null || callData === void 0 ? void 0 : callData.roomId);\n    onEndCall();\n  };\n\n  // Format call duration\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Toggle fullscreen\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n  if (callError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-red-600 mb-2\",\n          children: \"Call Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: callError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onEndCall,\n          className: \"w-full btn-primary\",\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `fixed inset-0 bg-black z-50 ${isFullscreen ? '' : 'p-4'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full h-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: isIncoming ? `Call from ${callData === null || callData === void 0 ? void 0 : (_callData$caller = callData.caller) === null || _callData$caller === void 0 ? void 0 : _callData$caller.name}` : `Calling ${(callData === null || callData === void 0 ? void 0 : (_callData$receiver = callData.receiver) === null || _callData$receiver === void 0 ? void 0 : _callData$receiver.name) || 'Doctor'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm opacity-75\",\n              children: isCallActive ? formatDuration(callDuration) : connectionState\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleFullscreen,\n            className: \"p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors\",\n            children: isFullscreen ? /*#__PURE__*/_jsxDEV(FiMinimize2, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(FiMaximize2, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          ref: remoteVideoRef,\n          autoPlay: true,\n          playsInline: true,\n          className: \"w-full h-full object-cover\",\n          style: {\n            transform: 'scaleX(-1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), !remoteStream && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center justify-center bg-gray-900\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-semibold\",\n                children: (callData === null || callData === void 0 ? void 0 : (_callData$caller2 = callData.caller) === null || _callData$caller2 === void 0 ? void 0 : (_callData$caller2$nam = _callData$caller2.name) === null || _callData$caller2$nam === void 0 ? void 0 : _callData$caller2$nam.charAt(0)) || (callData === null || callData === void 0 ? void 0 : (_callData$receiver2 = callData.receiver) === null || _callData$receiver2 === void 0 ? void 0 : (_callData$receiver2$n = _callData$receiver2.name) === null || _callData$receiver2$n === void 0 ? void 0 : _callData$receiver2$n.charAt(0)) || 'U'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg\",\n              children: isIncoming ? 'Connecting...' : 'Waiting for response...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this), (callData === null || callData === void 0 ? void 0 : callData.callType) === 'video' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-20 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-white/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: localVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            muted: true,\n            className: \"w-full h-full object-cover\",\n            style: {\n              transform: 'scaleX(-1)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), !isVideoEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-800 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(FiVideoOff, {\n              className: \"text-white\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleAudio,\n            className: `p-4 rounded-full transition-colors ${isAudioEnabled ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-red-600 hover:bg-red-700 text-white'}`,\n            children: isAudioEnabled ? /*#__PURE__*/_jsxDEV(FiMic, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(FiMicOff, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), (callData === null || callData === void 0 ? void 0 : callData.callType) === 'video' && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleVideo,\n            className: `p-4 rounded-full transition-colors ${isVideoEnabled ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-red-600 hover:bg-red-700 text-white'}`,\n            children: isVideoEnabled ? /*#__PURE__*/_jsxDEV(FiVideo, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(FiVideoOff, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), isIncoming && !isCallActive && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAnswerCall,\n            className: \"p-4 rounded-full bg-green-600 hover:bg-green-700 text-white transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FiPhone, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleEndCall,\n            className: \"p-4 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FiPhoneOff, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white text-sm opacity-75\",\n            children: [!isCallActive && isIncoming && 'Incoming call', !isCallActive && !isIncoming && 'Calling...', isCallActive && `Connected • ${formatDuration(callDuration)}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"W07KfJmVeO2eTq5aKDNioUcNdDE=\", false, function () {\n  return [useSocket, useWebRTC];\n});\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiVideo", "FiVideoOff", "FiMic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FiPhone", "FiPhoneOff", "FiMaximize2", "FiMinimize2", "useWebRTC", "useSocket", "jsxDEV", "_jsxDEV", "VideoCall", "callData", "onEndCall", "isIncoming", "_s", "_callData$caller", "_callData$receiver", "_callData$caller2", "_callData$caller2$nam", "_callData$receiver2", "_callData$receiver2$n", "isFullscreen", "setIsFullscreen", "callDuration", "setCallDuration", "callStartTime", "setCallStartTime", "endCall", "socketEndCall", "localStream", "remoteStream", "isCallActive", "isVideoEnabled", "isAudioEnabled", "callError", "connectionState", "localVideoRef", "remoteVideoRef", "startCall", "answerCall", "toggleVideo", "toggleAudio", "roomId", "interval", "setInterval", "Math", "floor", "Date", "now", "clearInterval", "callType", "handleAnswerCall", "handleEndCall", "callId", "formatDuration", "seconds", "mins", "secs", "toString", "padStart", "toggleFullscreen", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "caller", "name", "receiver", "size", "ref", "autoPlay", "playsInline", "style", "transform", "char<PERSON>t", "muted", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/VideoCall.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FiVideo, FiVideoOff, FiMic, FiMicOff, FiPhone, FiPhoneOff, FiMaximize2, FiMinimize2 } from 'react-icons/fi';\nimport useWebRTC from '../hooks/useWebRTC';\nimport { useSocket } from '../context/SocketContext';\n\nconst VideoCall = ({ callData, onEndCall, isIncoming = false }) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [callDuration, setCallDuration] = useState(0);\n  const [callStartTime, setCallStartTime] = useState(null);\n\n  const { endCall: socketEndCall } = useSocket();\n  \n  const {\n    localStream,\n    remoteStream,\n    isCallActive,\n    isVideoEnabled,\n    isAudioEnabled,\n    callError,\n    connectionState,\n    localVideoRef,\n    remoteVideoRef,\n    startCall,\n    answerCall,\n    endCall,\n    toggleVideo,\n    toggleAudio\n  } = useWebRTC(callData?.roomId, !isIncoming);\n\n  // Timer for call duration\n  useEffect(() => {\n    let interval;\n    if (isCallActive && callStartTime) {\n      interval = setInterval(() => {\n        setCallDuration(Math.floor((Date.now() - callStartTime) / 1000));\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [isCallActive, callStartTime]);\n\n  // Start call when component mounts (for outgoing calls)\n  useEffect(() => {\n    if (!isIncoming && callData) {\n      startCall(callData.callType === 'video', true);\n      setCallStartTime(Date.now());\n    }\n  }, [isIncoming, callData, startCall]);\n\n  // Handle incoming call answer\n  const handleAnswerCall = () => {\n    answerCall(callData.callType === 'video', true);\n    setCallStartTime(Date.now());\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    socketEndCall(callData?.callId, callData?.roomId);\n    onEndCall();\n  };\n\n  // Format call duration\n  const formatDuration = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Toggle fullscreen\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  if (callError) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n          <h3 className=\"text-lg font-semibold text-red-600 mb-2\">Call Error</h3>\n          <p className=\"text-gray-600 mb-4\">{callError}</p>\n          <button\n            onClick={onEndCall}\n            className=\"w-full btn-primary\"\n          >\n            Close\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`fixed inset-0 bg-black z-50 ${isFullscreen ? '' : 'p-4'}`}>\n      <div className=\"relative w-full h-full flex flex-col\">\n        {/* Header */}\n        <div className=\"absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent p-4\">\n          <div className=\"flex justify-between items-center text-white\">\n            <div>\n              <h3 className=\"text-lg font-semibold\">\n                {isIncoming ? `Call from ${callData?.caller?.name}` : `Calling ${callData?.receiver?.name || 'Doctor'}`}\n              </h3>\n              <p className=\"text-sm opacity-75\">\n                {isCallActive ? formatDuration(callDuration) : connectionState}\n              </p>\n            </div>\n            <button\n              onClick={toggleFullscreen}\n              className=\"p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors\"\n            >\n              {isFullscreen ? <FiMinimize2 size={20} /> : <FiMaximize2 size={20} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Video Container */}\n        <div className=\"flex-1 relative\">\n          {/* Remote Video (Main) */}\n          <video\n            ref={remoteVideoRef}\n            autoPlay\n            playsInline\n            className=\"w-full h-full object-cover\"\n            style={{ transform: 'scaleX(-1)' }}\n          />\n          \n          {/* Remote Video Placeholder */}\n          {!remoteStream && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900\">\n              <div className=\"text-center text-white\">\n                <div className=\"w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-semibold\">\n                    {callData?.caller?.name?.charAt(0) || callData?.receiver?.name?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <p className=\"text-lg\">\n                  {isIncoming ? 'Connecting...' : 'Waiting for response...'}\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Local Video (Picture-in-Picture) */}\n          {callData?.callType === 'video' && (\n            <div className=\"absolute top-20 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-white/20\">\n              <video\n                ref={localVideoRef}\n                autoPlay\n                playsInline\n                muted\n                className=\"w-full h-full object-cover\"\n                style={{ transform: 'scaleX(-1)' }}\n              />\n              {!isVideoEnabled && (\n                <div className=\"absolute inset-0 bg-gray-800 flex items-center justify-center\">\n                  <FiVideoOff className=\"text-white\" size={20} />\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Controls */}\n        <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-6\">\n          <div className=\"flex justify-center items-center space-x-6\">\n            {/* Audio Toggle */}\n            <button\n              onClick={toggleAudio}\n              className={`p-4 rounded-full transition-colors ${\n                isAudioEnabled \n                  ? 'bg-gray-700 hover:bg-gray-600 text-white' \n                  : 'bg-red-600 hover:bg-red-700 text-white'\n              }`}\n            >\n              {isAudioEnabled ? <FiMic size={24} /> : <FiMicOff size={24} />}\n            </button>\n\n            {/* Video Toggle (only for video calls) */}\n            {callData?.callType === 'video' && (\n              <button\n                onClick={toggleVideo}\n                className={`p-4 rounded-full transition-colors ${\n                  isVideoEnabled \n                    ? 'bg-gray-700 hover:bg-gray-600 text-white' \n                    : 'bg-red-600 hover:bg-red-700 text-white'\n                }`}\n              >\n                {isVideoEnabled ? <FiVideo size={24} /> : <FiVideoOff size={24} />}\n              </button>\n            )}\n\n            {/* Answer Call (for incoming calls) */}\n            {isIncoming && !isCallActive && (\n              <button\n                onClick={handleAnswerCall}\n                className=\"p-4 rounded-full bg-green-600 hover:bg-green-700 text-white transition-colors\"\n              >\n                <FiPhone size={24} />\n              </button>\n            )}\n\n            {/* End Call */}\n            <button\n              onClick={handleEndCall}\n              className=\"p-4 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors\"\n            >\n              <FiPhoneOff size={24} />\n            </button>\n          </div>\n\n          {/* Call Status */}\n          <div className=\"text-center mt-4\">\n            <p className=\"text-white text-sm opacity-75\">\n              {!isCallActive && isIncoming && 'Incoming call'}\n              {!isCallActive && !isIncoming && 'Calling...'}\n              {isCallActive && `Connected • ${formatDuration(callDuration)}`}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoCall;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,gBAAgB;AACpH,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,UAAU,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;EACjE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAM;IAAE+B,OAAO,EAAEC;EAAc,CAAC,GAAGrB,SAAS,CAAC,CAAC;EAE9C,MAAM;IACJsB,WAAW;IACXC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTC,eAAe;IACfC,aAAa;IACbC,cAAc;IACdC,SAAS;IACTC,UAAU;IACVZ,OAAO;IACPa,WAAW;IACXC;EACF,CAAC,GAAGnC,SAAS,CAACK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+B,MAAM,EAAE,CAAC7B,UAAU,CAAC;;EAE5C;EACAhB,SAAS,CAAC,MAAM;IACd,IAAI8C,QAAQ;IACZ,IAAIZ,YAAY,IAAIN,aAAa,EAAE;MACjCkB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BpB,eAAe,CAACqB,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGvB,aAAa,IAAI,IAAI,CAAC,CAAC;MAClE,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAMwB,aAAa,CAACN,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACZ,YAAY,EAAEN,aAAa,CAAC,CAAC;;EAEjC;EACA5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,UAAU,IAAIF,QAAQ,EAAE;MAC3B2B,SAAS,CAAC3B,QAAQ,CAACuC,QAAQ,KAAK,OAAO,EAAE,IAAI,CAAC;MAC9CxB,gBAAgB,CAACqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACnC,UAAU,EAAEF,QAAQ,EAAE2B,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC7BZ,UAAU,CAAC5B,QAAQ,CAACuC,QAAQ,KAAK,OAAO,EAAE,IAAI,CAAC;IAC/CxB,gBAAgB,CAACqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BzB,OAAO,CAAC,CAAC;IACTC,aAAa,CAACjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0C,MAAM,EAAE1C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+B,MAAM,CAAC;IACjD9B,SAAS,CAAC,CAAC;EACb,CAAC;;EAED;EACA,MAAM0C,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,IAAI,GAAGX,IAAI,CAACC,KAAK,CAACS,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtC,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,IAAIa,SAAS,EAAE;IACb,oBACEzB,OAAA;MAAKoD,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFrD,OAAA;QAAKoD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DrD,OAAA;UAAIoD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEzD,OAAA;UAAGoD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAE5B;QAAS;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDzD,OAAA;UACE0D,OAAO,EAAEvD,SAAU;UACnBiD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAE,+BAA+BxC,YAAY,GAAG,EAAE,GAAG,KAAK,EAAG;IAAAyC,QAAA,eACzErD,OAAA;MAAKoD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBAEnDrD,OAAA;QAAKoD,SAAS,EAAC,sFAAsF;QAAAC,QAAA,eACnGrD,OAAA;UAAKoD,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAIoD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAClCjD,UAAU,GAAG,aAAaF,QAAQ,aAARA,QAAQ,wBAAAI,gBAAA,GAARJ,QAAQ,CAAEyD,MAAM,cAAArD,gBAAA,uBAAhBA,gBAAA,CAAkBsD,IAAI,EAAE,GAAG,WAAW,CAAA1D,QAAQ,aAARA,QAAQ,wBAAAK,kBAAA,GAARL,QAAQ,CAAE2D,QAAQ,cAAAtD,kBAAA,uBAAlBA,kBAAA,CAAoBqD,IAAI,KAAI,QAAQ;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC,eACLzD,OAAA;cAAGoD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC9B/B,YAAY,GAAGuB,cAAc,CAAC/B,YAAY,CAAC,GAAGY;YAAe;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNzD,OAAA;YACE0D,OAAO,EAAEP,gBAAiB;YAC1BC,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAE3EzC,YAAY,gBAAGZ,OAAA,CAACJ,WAAW;cAACkE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACL,WAAW;cAACmE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BrD,OAAA;UACE+D,GAAG,EAAEnC,cAAe;UACpBoC,QAAQ;UACRC,WAAW;UACXb,SAAS,EAAC,4BAA4B;UACtCc,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAa;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAGD,CAACpC,YAAY,iBACZrB,OAAA;UAAKoD,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAC5ErD,OAAA;YAAKoD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCrD,OAAA;cAAKoD,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FrD,OAAA;gBAAMoD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC,CAAAnD,QAAQ,aAARA,QAAQ,wBAAAM,iBAAA,GAARN,QAAQ,CAAEyD,MAAM,cAAAnD,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBoD,IAAI,cAAAnD,qBAAA,uBAAtBA,qBAAA,CAAwB2D,MAAM,CAAC,CAAC,CAAC,MAAIlE,QAAQ,aAARA,QAAQ,wBAAAQ,mBAAA,GAARR,QAAQ,CAAE2D,QAAQ,cAAAnD,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBkD,IAAI,cAAAjD,qBAAA,uBAAxBA,qBAAA,CAA0ByD,MAAM,CAAC,CAAC,CAAC,KAAI;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNzD,OAAA;cAAGoD,SAAS,EAAC,SAAS;cAAAC,QAAA,EACnBjD,UAAU,GAAG,eAAe,GAAG;YAAyB;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA,CAAAvD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuC,QAAQ,MAAK,OAAO,iBAC7BzC,OAAA;UAAKoD,SAAS,EAAC,mGAAmG;UAAAC,QAAA,gBAChHrD,OAAA;YACE+D,GAAG,EAAEpC,aAAc;YACnBqC,QAAQ;YACRC,WAAW;YACXI,KAAK;YACLjB,SAAS,EAAC,4BAA4B;YACtCc,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAa;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,EACD,CAAClC,cAAc,iBACdvB,OAAA;YAAKoD,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5ErD,OAAA,CAACV,UAAU;cAAC8D,SAAS,EAAC,YAAY;cAACU,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,oFAAoF;QAAAC,QAAA,gBACjGrD,OAAA;UAAKoD,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzDrD,OAAA;YACE0D,OAAO,EAAE1B,WAAY;YACrBoB,SAAS,EAAE,sCACT5B,cAAc,GACV,0CAA0C,GAC1C,wCAAwC,EAC3C;YAAA6B,QAAA,EAEF7B,cAAc,gBAAGxB,OAAA,CAACT,KAAK;cAACuE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACR,QAAQ;cAACsE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,EAGR,CAAAvD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuC,QAAQ,MAAK,OAAO,iBAC7BzC,OAAA;YACE0D,OAAO,EAAE3B,WAAY;YACrBqB,SAAS,EAAE,sCACT7B,cAAc,GACV,0CAA0C,GAC1C,wCAAwC,EAC3C;YAAA8B,QAAA,EAEF9B,cAAc,gBAAGvB,OAAA,CAACX,OAAO;cAACyE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACV,UAAU;cAACwE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACT,EAGArD,UAAU,IAAI,CAACkB,YAAY,iBAC1BtB,OAAA;YACE0D,OAAO,EAAEhB,gBAAiB;YAC1BU,SAAS,EAAC,+EAA+E;YAAAC,QAAA,eAEzFrD,OAAA,CAACP,OAAO;cAACqE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACT,eAGDzD,OAAA;YACE0D,OAAO,EAAEf,aAAc;YACvBS,SAAS,EAAC,2EAA2E;YAAAC,QAAA,eAErFrD,OAAA,CAACN,UAAU;cAACoE,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BrD,OAAA;YAAGoD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,GACzC,CAAC/B,YAAY,IAAIlB,UAAU,IAAI,eAAe,EAC9C,CAACkB,YAAY,IAAI,CAAClB,UAAU,IAAI,YAAY,EAC5CkB,YAAY,IAAI,eAAeuB,cAAc,CAAC/B,YAAY,CAAC,EAAE;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAvNIJ,SAAS;EAAA,QAKsBH,SAAS,EAiBxCD,SAAS;AAAA;AAAAyE,EAAA,GAtBTrE,SAAS;AAyNf,eAAeA,SAAS;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}