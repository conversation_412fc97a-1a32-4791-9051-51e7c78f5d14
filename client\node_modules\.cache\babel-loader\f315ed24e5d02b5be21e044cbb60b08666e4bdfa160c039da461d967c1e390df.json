{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\n\n// Components\nimport Navbar from './components/Navbar';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport DoctorProfilePage from './pages/DoctorProfilePage';\nimport PatientDashboard from './pages/PatientDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/doctor/:id\",\n            element: /*#__PURE__*/_jsxDEV(DoctorProfilePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/patient-dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredRole: \"patient\",\n              children: /*#__PURE__*/_jsxDEV(PatientDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/doctor-dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredRole: \"doctor\",\n              children: /*#__PURE__*/_jsxDEV(DoctorDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83D\\uDD0D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-900 mb-2\",\n                  children: \"Page Not Found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-4\",\n                  children: \"The page you're looking for doesn't exist.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"/\",\n                  className: \"btn-primary\",\n                  children: \"Go Home\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ProtectedRoute", "HomePage", "LoginPage", "RegisterPage", "DoctorProfilePage", "PatientDashboard", "DoctorDashboard", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "requiredRole", "href", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\n\n// Components\nimport Navbar from './components/Navbar';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport DoctorProfilePage from './pages/DoctorProfilePage';\nimport PatientDashboard from './pages/PatientDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Navbar />\n          <Routes>\n            {/* Public Routes */}\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/register\" element={<RegisterPage />} />\n            <Route path=\"/doctor/:id\" element={<DoctorProfilePage />} />\n\n            {/* Protected Routes */}\n            <Route \n              path=\"/patient-dashboard\" \n              element={\n                <ProtectedRoute requiredRole=\"patient\">\n                  <PatientDashboard />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/doctor-dashboard\" \n              element={\n                <ProtectedRoute requiredRole=\"doctor\">\n                  <DoctorDashboard />\n                </ProtectedRoute>\n              } \n            />\n\n            {/* 404 Route */}\n            <Route \n              path=\"*\" \n              element={\n                <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-6xl mb-4\">🔍</div>\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Page Not Found</h2>\n                    <p className=\"text-gray-600 mb-4\">The page you're looking for doesn't exist.</p>\n                    <a href=\"/\" className=\"btn-primary\">\n                      Go Home\n                    </a>\n                  </div>\n                </div>\n              } \n            />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,YAAY;IAAAY,QAAA,eACXF,OAAA,CAACb,MAAM;MAAAe,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACT,MAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA,CAACZ,MAAM;UAAAc,QAAA,gBAELF,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACP,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAET,OAAA,CAACN,SAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAET,OAAA,CAACL,YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDP,OAAA,CAACX,KAAK;YAACmB,IAAI,EAAC,aAAa;YAACC,OAAO,eAAET,OAAA,CAACJ,iBAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG5DP,OAAA,CAACX,KAAK;YACJmB,IAAI,EAAC,oBAAoB;YACzBC,OAAO,eACLT,OAAA,CAACR,cAAc;cAACkB,YAAY,EAAC,SAAS;cAAAR,QAAA,eACpCF,OAAA,CAACH,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFP,OAAA,CAACX,KAAK;YACJmB,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eACLT,OAAA,CAACR,cAAc;cAACkB,YAAY,EAAC,QAAQ;cAAAR,QAAA,eACnCF,OAAA,CAACF,eAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFP,OAAA,CAACX,KAAK;YACJmB,IAAI,EAAC,GAAG;YACRC,OAAO,eACLT,OAAA;cAAKG,SAAS,EAAC,0DAA0D;cAAAD,QAAA,eACvEF,OAAA;gBAAKG,SAAS,EAAC,aAAa;gBAAAD,QAAA,gBAC1BF,OAAA;kBAAKG,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCP,OAAA;kBAAIG,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEP,OAAA;kBAAGG,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,EAAC;gBAA0C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChFP,OAAA;kBAAGW,IAAI,EAAC,GAAG;kBAACR,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAEpC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACK,EAAA,GApDQX,GAAG;AAsDZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}