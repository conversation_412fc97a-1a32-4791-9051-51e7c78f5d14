{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API calls\nexport const authAPI = {\n  register: userData => api.post('/auth/register', userData),\n  login: credentials => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me')\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: params => api.get('/doctors', {\n    params\n  }),\n  getDoctorById: id => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: data => api.put('/doctors/profile', data),\n  updateAvailability: availability => api.put('/doctors/availability', {\n    availability\n  })\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) => api.get(`/appointments/available-slots/${doctorId}`, {\n    params: {\n      date\n    }\n  }),\n  bookAppointment: appointmentData => api.post('/appointments/book', appointmentData),\n  getMyAppointments: params => api.get('/appointments/my-appointments', {\n    params\n  }),\n  cancelAppointment: (id, reason) => api.patch(`/appointments/${id}/cancel`, {\n    cancellationReason: reason\n  }),\n  updateAppointmentStatus: (id, status, notes) => api.patch(`/appointments/${id}/status`, {\n    status,\n    notes\n  })\n};\n\n// Chat API calls\nexport const chatAPI = {\n  getConversations: () => api.get('/chat/conversations'),\n  getMessages: (userId, params) => api.get(`/chat/messages/${userId}`, {\n    params\n  }),\n  sendMessage: messageData => api.post('/chat/send', messageData),\n  getAppointmentMessages: appointmentId => api.get(`/chat/appointment-messages/${appointmentId}`),\n  deleteMessage: messageId => api.delete(`/chat/messages/${messageId}`)\n};\n\n// Call API calls\nexport const callAPI = {\n  getCallHistory: params => api.get('/calls/history', {\n    params\n  }),\n  getCallDetails: callId => api.get(`/calls/${callId}`),\n  rateCall: callData => api.post('/calls/rate', callData),\n  getCallStats: () => api.get('/calls/stats/summary'),\n  initiateEmergencyCall: description => api.post('/calls/emergency', {\n    description\n  }),\n  getActiveCalls: () => api.get('/calls/active/list')\n};\n\n// Helpline API calls\nexport const helplineAPI = {\n  createTicket: ticketData => api.post('/helpline/tickets', ticketData),\n  getTickets: params => api.get('/helpline/tickets', {\n    params\n  }),\n  getTicketDetails: ticketId => api.get(`/helpline/tickets/${ticketId}`),\n  addTicketMessage: (ticketId, messageData) => api.post(`/helpline/tickets/${ticketId}/messages`, messageData),\n  updateTicketStatus: (ticketId, status) => api.patch(`/helpline/tickets/${ticketId}/status`, {\n    status\n  }),\n  rateSupport: (ticketId, ratingData) => api.post(`/helpline/tickets/${ticketId}/rate`, ratingData),\n  getCategories: () => api.get('/helpline/categories'),\n  getQuickHelp: () => api.get('/helpline/quick-help')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "register", "userData", "post", "login", "credentials", "getProfile", "get", "doctor<PERSON><PERSON>", "getAllDoctors", "params", "getDoctorById", "id", "getSpecialties", "getMyProfile", "updateProfile", "data", "put", "updateAvailability", "availability", "appointmentAPI", "getAvailableSlots", "doctorId", "date", "bookAppointment", "appointmentData", "getMyAppointments", "cancelAppointment", "reason", "patch", "cancellationReason", "updateAppointmentStatus", "notes", "chatAPI", "getConversations", "getMessages", "userId", "sendMessage", "messageData", "getAppointmentMessages", "appointmentId", "deleteMessage", "messageId", "delete", "callAPI", "getCallHistory", "getCallDetails", "callId", "rateCall", "callData", "getCallStats", "initiateEmergencyCall", "description", "getActiveCalls", "helplineAPI", "createTicket", "ticketData", "getTickets", "getTicketDetails", "ticketId", "addTicketMessage", "updateTicketStatus", "rateSupport", "ratingData", "getCategories", "getQuickHelp"], "sources": ["E:/03/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API calls\nexport const authAPI = {\n  register: (userData) => api.post('/auth/register', userData),\n  login: (credentials) => api.post('/auth/login', credentials),\n  getProfile: () => api.get('/auth/me'),\n};\n\n// Doctor API calls\nexport const doctorAPI = {\n  getAllDoctors: (params) => api.get('/doctors', { params }),\n  getDoctorById: (id) => api.get(`/doctors/${id}`),\n  getSpecialties: () => api.get('/doctors/specialties'),\n  getMyProfile: () => api.get('/doctors/profile/me'),\n  updateProfile: (data) => api.put('/doctors/profile', data),\n  updateAvailability: (availability) => api.put('/doctors/availability', { availability }),\n};\n\n// Appointment API calls\nexport const appointmentAPI = {\n  getAvailableSlots: (doctorId, date) =>\n    api.get(`/appointments/available-slots/${doctorId}`, { params: { date } }),\n  bookAppointment: (appointmentData) => api.post('/appointments/book', appointmentData),\n  getMyAppointments: (params) => api.get('/appointments/my-appointments', { params }),\n  cancelAppointment: (id, reason) =>\n    api.patch(`/appointments/${id}/cancel`, { cancellationReason: reason }),\n  updateAppointmentStatus: (id, status, notes) =>\n    api.patch(`/appointments/${id}/status`, { status, notes }),\n};\n\n// Chat API calls\nexport const chatAPI = {\n  getConversations: () => api.get('/chat/conversations'),\n  getMessages: (userId, params) => api.get(`/chat/messages/${userId}`, { params }),\n  sendMessage: (messageData) => api.post('/chat/send', messageData),\n  getAppointmentMessages: (appointmentId) => api.get(`/chat/appointment-messages/${appointmentId}`),\n  deleteMessage: (messageId) => api.delete(`/chat/messages/${messageId}`),\n};\n\n// Call API calls\nexport const callAPI = {\n  getCallHistory: (params) => api.get('/calls/history', { params }),\n  getCallDetails: (callId) => api.get(`/calls/${callId}`),\n  rateCall: (callData) => api.post('/calls/rate', callData),\n  getCallStats: () => api.get('/calls/stats/summary'),\n  initiateEmergencyCall: (description) => api.post('/calls/emergency', { description }),\n  getActiveCalls: () => api.get('/calls/active/list'),\n};\n\n// Helpline API calls\nexport const helplineAPI = {\n  createTicket: (ticketData) => api.post('/helpline/tickets', ticketData),\n  getTickets: (params) => api.get('/helpline/tickets', { params }),\n  getTicketDetails: (ticketId) => api.get(`/helpline/tickets/${ticketId}`),\n  addTicketMessage: (ticketId, messageData) => api.post(`/helpline/tickets/${ticketId}/messages`, messageData),\n  updateTicketStatus: (ticketId, status) => api.patch(`/helpline/tickets/${ticketId}/status`, { status }),\n  rateSupport: (ticketId, ratingData) => api.post(`/helpline/tickets/${ticketId}/rate`, ratingData),\n  getCategories: () => api.get('/helpline/categories'),\n  getQuickHelp: () => api.get('/helpline/quick-help'),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAN,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAK3B,GAAG,CAAC4B,IAAI,CAAC,gBAAgB,EAAED,QAAQ,CAAC;EAC5DE,KAAK,EAAGC,WAAW,IAAK9B,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAEE,WAAW,CAAC;EAC5DC,UAAU,EAAEA,CAAA,KAAM/B,GAAG,CAACgC,GAAG,CAAC,UAAU;AACtC,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,aAAa,EAAGC,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,UAAU,EAAE;IAAEG;EAAO,CAAC,CAAC;EAC1DC,aAAa,EAAGC,EAAE,IAAKrC,GAAG,CAACgC,GAAG,CAAC,YAAYK,EAAE,EAAE,CAAC;EAChDC,cAAc,EAAEA,CAAA,KAAMtC,GAAG,CAACgC,GAAG,CAAC,sBAAsB,CAAC;EACrDO,YAAY,EAAEA,CAAA,KAAMvC,GAAG,CAACgC,GAAG,CAAC,qBAAqB,CAAC;EAClDQ,aAAa,EAAGC,IAAI,IAAKzC,GAAG,CAAC0C,GAAG,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAC1DE,kBAAkB,EAAGC,YAAY,IAAK5C,GAAG,CAAC0C,GAAG,CAAC,uBAAuB,EAAE;IAAEE;EAAa,CAAC;AACzF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,iBAAiB,EAAEA,CAACC,QAAQ,EAAEC,IAAI,KAChChD,GAAG,CAACgC,GAAG,CAAC,iCAAiCe,QAAQ,EAAE,EAAE;IAAEZ,MAAM,EAAE;MAAEa;IAAK;EAAE,CAAC,CAAC;EAC5EC,eAAe,EAAGC,eAAe,IAAKlD,GAAG,CAAC4B,IAAI,CAAC,oBAAoB,EAAEsB,eAAe,CAAC;EACrFC,iBAAiB,EAAGhB,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,+BAA+B,EAAE;IAAEG;EAAO,CAAC,CAAC;EACnFiB,iBAAiB,EAAEA,CAACf,EAAE,EAAEgB,MAAM,KAC5BrD,GAAG,CAACsD,KAAK,CAAC,iBAAiBjB,EAAE,SAAS,EAAE;IAAEkB,kBAAkB,EAAEF;EAAO,CAAC,CAAC;EACzEG,uBAAuB,EAAEA,CAACnB,EAAE,EAAEjB,MAAM,EAAEqC,KAAK,KACzCzD,GAAG,CAACsD,KAAK,CAAC,iBAAiBjB,EAAE,SAAS,EAAE;IAAEjB,MAAM;IAAEqC;EAAM,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG;EACrBC,gBAAgB,EAAEA,CAAA,KAAM3D,GAAG,CAACgC,GAAG,CAAC,qBAAqB,CAAC;EACtD4B,WAAW,EAAEA,CAACC,MAAM,EAAE1B,MAAM,KAAKnC,GAAG,CAACgC,GAAG,CAAC,kBAAkB6B,MAAM,EAAE,EAAE;IAAE1B;EAAO,CAAC,CAAC;EAChF2B,WAAW,EAAGC,WAAW,IAAK/D,GAAG,CAAC4B,IAAI,CAAC,YAAY,EAAEmC,WAAW,CAAC;EACjEC,sBAAsB,EAAGC,aAAa,IAAKjE,GAAG,CAACgC,GAAG,CAAC,8BAA8BiC,aAAa,EAAE,CAAC;EACjGC,aAAa,EAAGC,SAAS,IAAKnE,GAAG,CAACoE,MAAM,CAAC,kBAAkBD,SAAS,EAAE;AACxE,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrBC,cAAc,EAAGnC,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,gBAAgB,EAAE;IAAEG;EAAO,CAAC,CAAC;EACjEoC,cAAc,EAAGC,MAAM,IAAKxE,GAAG,CAACgC,GAAG,CAAC,UAAUwC,MAAM,EAAE,CAAC;EACvDC,QAAQ,EAAGC,QAAQ,IAAK1E,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAE8C,QAAQ,CAAC;EACzDC,YAAY,EAAEA,CAAA,KAAM3E,GAAG,CAACgC,GAAG,CAAC,sBAAsB,CAAC;EACnD4C,qBAAqB,EAAGC,WAAW,IAAK7E,GAAG,CAAC4B,IAAI,CAAC,kBAAkB,EAAE;IAAEiD;EAAY,CAAC,CAAC;EACrFC,cAAc,EAAEA,CAAA,KAAM9E,GAAG,CAACgC,GAAG,CAAC,oBAAoB;AACpD,CAAC;;AAED;AACA,OAAO,MAAM+C,WAAW,GAAG;EACzBC,YAAY,EAAGC,UAAU,IAAKjF,GAAG,CAAC4B,IAAI,CAAC,mBAAmB,EAAEqD,UAAU,CAAC;EACvEC,UAAU,EAAG/C,MAAM,IAAKnC,GAAG,CAACgC,GAAG,CAAC,mBAAmB,EAAE;IAAEG;EAAO,CAAC,CAAC;EAChEgD,gBAAgB,EAAGC,QAAQ,IAAKpF,GAAG,CAACgC,GAAG,CAAC,qBAAqBoD,QAAQ,EAAE,CAAC;EACxEC,gBAAgB,EAAEA,CAACD,QAAQ,EAAErB,WAAW,KAAK/D,GAAG,CAAC4B,IAAI,CAAC,qBAAqBwD,QAAQ,WAAW,EAAErB,WAAW,CAAC;EAC5GuB,kBAAkB,EAAEA,CAACF,QAAQ,EAAEhE,MAAM,KAAKpB,GAAG,CAACsD,KAAK,CAAC,qBAAqB8B,QAAQ,SAAS,EAAE;IAAEhE;EAAO,CAAC,CAAC;EACvGmE,WAAW,EAAEA,CAACH,QAAQ,EAAEI,UAAU,KAAKxF,GAAG,CAAC4B,IAAI,CAAC,qBAAqBwD,QAAQ,OAAO,EAAEI,UAAU,CAAC;EACjGC,aAAa,EAAEA,CAAA,KAAMzF,GAAG,CAACgC,GAAG,CAAC,sBAAsB,CAAC;EACpD0D,YAAY,EAAEA,CAAA,KAAM1F,GAAG,CAACgC,GAAG,CAAC,sBAAsB;AACpD,CAAC;AAED,eAAehC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}