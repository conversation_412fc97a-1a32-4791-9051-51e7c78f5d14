{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\pages\\\\DoctorProfilePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { doctorAPI } from '../services/api';\nimport BookingForm from '../components/BookingForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorProfilePage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [doctor, setDoctor] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [bookingSuccess, setBookingSuccess] = useState(false);\n  useEffect(() => {\n    fetchDoctorProfile();\n  }, [id]);\n  const fetchDoctorProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await doctorAPI.getDoctorById(id);\n      setDoctor(response.data.data.doctor);\n    } catch (error) {\n      setError('Doctor not found or not available');\n      console.error('Error fetching doctor:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBookingSuccess = () => {\n    setBookingSuccess(true);\n    setTimeout(() => {\n      setBookingSuccess(false);\n    }, 5000);\n  };\n  const formatAvailability = availability => {\n    return availability.filter(day => day.isAvailable).map(day => `${day.day}: ${day.startTime} - ${day.endTime}`).join(', ');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !doctor) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDE1E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Doctor Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          className: \"btn-primary\",\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [bookingSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-600 mr-3\",\n            children: \"\\u2705\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-green-800 font-semibold\",\n              children: \"Appointment Booked Successfully!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-700 text-sm\",\n              children: \"Your appointment has been booked. You can view it in your dashboard.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: doctor.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: doctor.profileImage,\n                  alt: doctor.user.name,\n                  className: \"w-24 h-24 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl font-semibold text-gray-600\",\n                  children: doctor.user.name.charAt(0).toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: [\"Dr. \", doctor.user.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium\",\n                    children: doctor.specialty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [doctor.experienceInYears, \" years experience\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600 font-medium\",\n                    children: \"Available for appointments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: [\"$\", doctor.consultationFee, /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-base font-normal text-gray-600 ml-1\",\n                    children: \"consultation fee\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Qualifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed\",\n                children: doctor.qualifications\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), doctor.about && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed\",\n                children: doctor.about\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Availability\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-2 gap-4\",\n                children: doctor.availability.map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-lg border ${day.isAvailable ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900\",\n                      children: day.day\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 25\n                    }, this), day.isAvailable ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-green-600 text-sm\",\n                      children: [day.startTime, \" - \", day.endTime]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this)\n                }, day.day, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t pt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"\\uD83D\\uDCE7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700\",\n                    children: doctor.user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sticky top-8\",\n            children: /*#__PURE__*/_jsxDEV(BookingForm, {\n              doctorId: doctor.user._id,\n              onBookingSuccess: handleBookingSuccess\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorProfilePage, \"/pVRh4FgvPZ55tNmjCDV1YzYOCQ=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = DoctorProfilePage;\nexport default DoctorProfilePage;\nvar _c;\n$RefreshReg$(_c, \"DoctorProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "doctor<PERSON><PERSON>", "BookingForm", "jsxDEV", "_jsxDEV", "DoctorProfilePage", "_s", "id", "navigate", "doctor", "setDoctor", "loading", "setLoading", "error", "setError", "bookingSuccess", "setBookingSuccess", "fetchDoctorProfile", "response", "getDoctorById", "data", "console", "handleBookingSuccess", "setTimeout", "formatAvailability", "availability", "filter", "day", "isAvailable", "map", "startTime", "endTime", "join", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "profileImage", "src", "alt", "user", "name", "char<PERSON>t", "toUpperCase", "specialty", "experienceInYears", "consultationFee", "qualifications", "about", "email", "doctorId", "_id", "onBookingSuccess", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/pages/DoctorProfilePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { doctorAPI } from '../services/api';\nimport BookingForm from '../components/BookingForm';\n\nconst DoctorProfilePage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [doctor, setDoctor] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [bookingSuccess, setBookingSuccess] = useState(false);\n\n  useEffect(() => {\n    fetchDoctorProfile();\n  }, [id]);\n\n  const fetchDoctorProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await doctorAPI.getDoctorById(id);\n      setDoctor(response.data.data.doctor);\n    } catch (error) {\n      setError('Doctor not found or not available');\n      console.error('Error fetching doctor:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBookingSuccess = () => {\n    setBookingSuccess(true);\n    setTimeout(() => {\n      setBookingSuccess(false);\n    }, 5000);\n  };\n\n  const formatAvailability = (availability) => {\n    return availability\n      .filter(day => day.isAvailable)\n      .map(day => `${day.day}: ${day.startTime} - ${day.endTime}`)\n      .join(', ');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  if (error || !doctor) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">😞</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Doctor Not Found</h2>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button \n            onClick={() => navigate('/')}\n            className=\"btn-primary\"\n          >\n            Back to Home\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Success Message */}\n        {bookingSuccess && (\n          <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"text-green-600 mr-3\">✅</div>\n              <div>\n                <h4 className=\"text-green-800 font-semibold\">Appointment Booked Successfully!</h4>\n                <p className=\"text-green-700 text-sm\">\n                  Your appointment has been booked. You can view it in your dashboard.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Doctor Information */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              {/* Doctor Header */}\n              <div className=\"flex items-start space-x-6 mb-6\">\n                <div className=\"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\">\n                  {doctor.profileImage ? (\n                    <img \n                      src={doctor.profileImage} \n                      alt={doctor.user.name}\n                      className=\"w-24 h-24 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <span className=\"text-3xl font-semibold text-gray-600\">\n                      {doctor.user.name.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n\n                <div className=\"flex-1\">\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    Dr. {doctor.user.name}\n                  </h1>\n                  \n                  <div className=\"flex items-center space-x-4 mb-3\">\n                    <span className=\"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium\">\n                      {doctor.specialty}\n                    </span>\n                    <span className=\"text-gray-600\">\n                      {doctor.experienceInYears} years experience\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 mb-4\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-green-600 font-medium\">Available for appointments</span>\n                  </div>\n\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${doctor.consultationFee}\n                    <span className=\"text-base font-normal text-gray-600 ml-1\">\n                      consultation fee\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Qualifications */}\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Qualifications</h3>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  {doctor.qualifications}\n                </p>\n              </div>\n\n              {/* About */}\n              {doctor.about && (\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">About</h3>\n                  <p className=\"text-gray-700 leading-relaxed\">\n                    {doctor.about}\n                  </p>\n                </div>\n              )}\n\n              {/* Availability */}\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Availability</h3>\n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  {doctor.availability.map((day) => (\n                    <div \n                      key={day.day}\n                      className={`p-3 rounded-lg border ${\n                        day.isAvailable \n                          ? 'bg-green-50 border-green-200' \n                          : 'bg-gray-50 border-gray-200'\n                      }`}\n                    >\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"font-medium text-gray-900\">{day.day}</span>\n                        {day.isAvailable ? (\n                          <span className=\"text-green-600 text-sm\">\n                            {day.startTime} - {day.endTime}\n                          </span>\n                        ) : (\n                          <span className=\"text-gray-500 text-sm\">Unavailable</span>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"border-t pt-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Contact Information</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-gray-500\">📧</span>\n                    <span className=\"text-gray-700\">{doctor.user.email}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Booking Form */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"sticky top-8\">\n              <BookingForm \n                doctorId={doctor.user._id} \n                onBookingSuccess={handleBookingSuccess}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE3DC,SAAS,CAAC,MAAM;IACdmB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;EAER,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMjB,SAAS,CAACkB,aAAa,CAACZ,EAAE,CAAC;MAClDG,SAAS,CAACQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACX,MAAM,CAAC;IACtC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,QAAQ,CAAC,mCAAmC,CAAC;MAC7CO,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,oBAAoB,GAAGA,CAAA,KAAM;IACjCN,iBAAiB,CAAC,IAAI,CAAC;IACvBO,UAAU,CAAC,MAAM;MACfP,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMQ,kBAAkB,GAAIC,YAAY,IAAK;IAC3C,OAAOA,YAAY,CAChBC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAC9BC,GAAG,CAACF,GAAG,IAAI,GAAGA,GAAG,CAACA,GAAG,KAAKA,GAAG,CAACG,SAAS,MAAMH,GAAG,CAACI,OAAO,EAAE,CAAC,CAC3DC,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;EAED,IAAIrB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK6B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9B,OAAA;QAAK6B,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,IAAIzB,KAAK,IAAI,CAACJ,MAAM,EAAE;IACpB,oBACEL,OAAA;MAAK6B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvClC,OAAA;UAAI6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ElC,OAAA;UAAG6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAErB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7ClC,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,GAAG,CAAE;UAC7ByB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eAC3C9B,OAAA;MAAK6B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,GAEpDnB,cAAc,iBACbX,OAAA;QAAK6B,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtE9B,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9B,OAAA;YAAK6B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5ClC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAI6B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFlC,OAAA;cAAG6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDlC,OAAA;QAAK6B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExC9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9B,OAAA;YAAK6B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEnB9B,OAAA;cAAK6B,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9B,OAAA;gBAAK6B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAC/FzB,MAAM,CAAC+B,YAAY,gBAClBpC,OAAA;kBACEqC,GAAG,EAAEhC,MAAM,CAAC+B,YAAa;kBACzBE,GAAG,EAAEjC,MAAM,CAACkC,IAAI,CAACC,IAAK;kBACtBX,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,gBAEFlC,OAAA;kBAAM6B,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACnDzB,MAAM,CAACkC,IAAI,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlC,OAAA;gBAAK6B,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB9B,OAAA;kBAAI6B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,GAAC,MAChD,EAACzB,MAAM,CAACkC,IAAI,CAACC,IAAI;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAELlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBAAM6B,SAAS,EAAC,4EAA4E;oBAAAC,QAAA,EACzFzB,MAAM,CAACsC;kBAAS;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPlC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAC5BzB,MAAM,CAACuC,iBAAiB,EAAC,mBAC5B;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBAAK6B,SAAS,EAAC;kBAAmC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDlC,OAAA;oBAAM6B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,GAC/C,EAACzB,MAAM,CAACwC,eAAe,eACxB7C,OAAA;oBAAM6B,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ElC,OAAA;gBAAG6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EACzCzB,MAAM,CAACyC;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EAGL7B,MAAM,CAAC0C,KAAK,iBACX/C,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElC,OAAA;gBAAG6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EACzCzB,MAAM,CAAC0C;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,eAGDlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ElC,OAAA;gBAAK6B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCzB,MAAM,CAACgB,YAAY,CAACI,GAAG,CAAEF,GAAG,iBAC3BvB,OAAA;kBAEE6B,SAAS,EAAE,yBACTN,GAAG,CAACC,WAAW,GACX,8BAA8B,GAC9B,4BAA4B,EAC/B;kBAAAM,QAAA,eAEH9B,OAAA;oBAAK6B,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD9B,OAAA;sBAAM6B,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEP,GAAG,CAACA;oBAAG;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAC3DX,GAAG,CAACC,WAAW,gBACdxB,OAAA;sBAAM6B,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,GACrCP,GAAG,CAACG,SAAS,EAAC,KAAG,EAACH,GAAG,CAACI,OAAO;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,gBAEPlC,OAAA;sBAAM6B,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC1D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC,GAhBDX,GAAG,CAACA,GAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBT,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFlC,OAAA;gBAAK6B,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxB9B,OAAA;kBAAK6B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzClC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEzB,MAAM,CAACkC,IAAI,CAACS;kBAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9B,OAAA;YAAK6B,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B9B,OAAA,CAACF,WAAW;cACVmD,QAAQ,EAAE5C,MAAM,CAACkC,IAAI,CAACW,GAAI;cAC1BC,gBAAgB,EAAEjC;YAAqB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3MID,iBAAiB;EAAA,QACNN,SAAS,EACPC,WAAW;AAAA;AAAAwD,EAAA,GAFxBnD,iBAAiB;AA6MvB,eAAeA,iBAAiB;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}