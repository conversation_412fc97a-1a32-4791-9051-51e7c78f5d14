{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport { useSocket } from '../context/SocketContext';\nconst useWebRTC = (roomId, isInitiator = false) => {\n  _s();\n  const [localStream, setLocalStream] = useState(null);\n  const [remoteStream, setRemoteStream] = useState(null);\n  const [isCallActive, setIsCallActive] = useState(false);\n  const [isVideoEnabled, setIsVideoEnabled] = useState(true);\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [callError, setCallError] = useState(null);\n  const [connectionState, setConnectionState] = useState('new');\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n  const {\n    socket,\n    sendWebRTCOffer,\n    sendWebRTCAnswer,\n    sendICECandidate\n  } = useSocket();\n\n  // ICE servers configuration\n  const iceServers = {\n    iceServers: [{\n      urls: 'stun:stun.l.google.com:19302'\n    }, {\n      urls: 'stun:stun1.l.google.com:19302'\n    }, {\n      urls: 'stun:stun2.l.google.com:19302'\n    }]\n  };\n\n  // Initialize peer connection\n  const initializePeerConnection = useCallback(() => {\n    if (peerConnectionRef.current) {\n      return peerConnectionRef.current;\n    }\n    const peerConnection = new RTCPeerConnection(iceServers);\n\n    // Handle ICE candidates\n    peerConnection.onicecandidate = event => {\n      if (event.candidate && roomId) {\n        sendICECandidate(roomId, event.candidate);\n      }\n    };\n\n    // Handle remote stream\n    peerConnection.ontrack = event => {\n      console.log('Received remote stream');\n      setRemoteStream(event.streams[0]);\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = event.streams[0];\n      }\n    };\n\n    // Handle connection state changes\n    peerConnection.onconnectionstatechange = () => {\n      setConnectionState(peerConnection.connectionState);\n      console.log('Connection state:', peerConnection.connectionState);\n      if (peerConnection.connectionState === 'connected') {\n        setIsCallActive(true);\n        setCallError(null);\n      } else if (peerConnection.connectionState === 'failed') {\n        setCallError('Connection failed');\n        setIsCallActive(false);\n      } else if (peerConnection.connectionState === 'disconnected') {\n        setIsCallActive(false);\n      }\n    };\n\n    // Handle ICE connection state changes\n    peerConnection.oniceconnectionstatechange = () => {\n      console.log('ICE connection state:', peerConnection.iceConnectionState);\n      if (peerConnection.iceConnectionState === 'failed') {\n        setCallError('ICE connection failed');\n      }\n    };\n    peerConnectionRef.current = peerConnection;\n    return peerConnection;\n  }, [roomId, sendICECandidate]);\n\n  // Get user media (camera and microphone)\n  const getUserMedia = useCallback(async (video = true, audio = true) => {\n    try {\n      const constraints = {\n        video: video ? {\n          width: {\n            ideal: 1280\n          },\n          height: {\n            ideal: 720\n          },\n          frameRate: {\n            ideal: 30\n          }\n        } : false,\n        audio: audio ? {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        } : false\n      };\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      setLocalStream(stream);\n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      return stream;\n    } catch (error) {\n      console.error('Error accessing media devices:', error);\n      setCallError('Could not access camera/microphone');\n      throw error;\n    }\n  }, []);\n\n  // Start call (for initiator)\n  const startCall = useCallback(async (video = true, audio = true) => {\n    try {\n      setCallError(null);\n\n      // Get user media\n      const stream = await getUserMedia(video, audio);\n\n      // Initialize peer connection\n      const peerConnection = initializePeerConnection();\n\n      // Add local stream to peer connection\n      stream.getTracks().forEach(track => {\n        peerConnection.addTrack(track, stream);\n      });\n\n      // Create and send offer\n      const offer = await peerConnection.createOffer();\n      await peerConnection.setLocalDescription(offer);\n      if (roomId) {\n        sendWebRTCOffer(roomId, offer);\n      }\n      setIsVideoEnabled(video);\n      setIsAudioEnabled(audio);\n    } catch (error) {\n      console.error('Error starting call:', error);\n      setCallError('Failed to start call');\n    }\n  }, [getUserMedia, initializePeerConnection, roomId, sendWebRTCOffer]);\n\n  // Answer call (for receiver)\n  const answerCall = useCallback(async (video = true, audio = true) => {\n    try {\n      setCallError(null);\n\n      // Get user media\n      const stream = await getUserMedia(video, audio);\n\n      // Initialize peer connection\n      const peerConnection = initializePeerConnection();\n\n      // Add local stream to peer connection\n      stream.getTracks().forEach(track => {\n        peerConnection.addTrack(track, stream);\n      });\n      setIsVideoEnabled(video);\n      setIsAudioEnabled(audio);\n    } catch (error) {\n      console.error('Error answering call:', error);\n      setCallError('Failed to answer call');\n    }\n  }, [getUserMedia, initializePeerConnection]);\n\n  // Handle incoming offer\n  const handleOffer = useCallback(async offer => {\n    try {\n      const peerConnection = peerConnectionRef.current || initializePeerConnection();\n      await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));\n      const answer = await peerConnection.createAnswer();\n      await peerConnection.setLocalDescription(answer);\n      if (roomId) {\n        sendWebRTCAnswer(roomId, answer);\n      }\n    } catch (error) {\n      console.error('Error handling offer:', error);\n      setCallError('Failed to handle incoming call');\n    }\n  }, [initializePeerConnection, roomId, sendWebRTCAnswer]);\n\n  // Handle incoming answer\n  const handleAnswer = useCallback(async answer => {\n    try {\n      const peerConnection = peerConnectionRef.current;\n      if (peerConnection) {\n        await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));\n      }\n    } catch (error) {\n      console.error('Error handling answer:', error);\n      setCallError('Failed to establish connection');\n    }\n  }, []);\n\n  // Handle incoming ICE candidate\n  const handleICECandidate = useCallback(async candidate => {\n    try {\n      const peerConnection = peerConnectionRef.current;\n      if (peerConnection) {\n        await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\n      }\n    } catch (error) {\n      console.error('Error handling ICE candidate:', error);\n    }\n  }, []);\n\n  // Toggle video\n  const toggleVideo = useCallback(() => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoEnabled(videoTrack.enabled);\n      }\n    }\n  }, []);\n\n  // Toggle audio\n  const toggleAudio = useCallback(() => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioEnabled(audioTrack.enabled);\n      }\n    }\n  }, []);\n\n  // End call\n  const endCall = useCallback(() => {\n    // Stop local stream\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => track.stop());\n    }\n\n    // Close peer connection\n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Reset states\n    setLocalStream(null);\n    setRemoteStream(null);\n    setIsCallActive(false);\n    setIsVideoEnabled(true);\n    setIsAudioEnabled(true);\n    setCallError(null);\n    setConnectionState('new');\n\n    // Clear video elements\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n    localStreamRef.current = null;\n  }, []);\n\n  // Socket event listeners\n  useEffect(() => {\n    if (!socket || !roomId) return;\n    const handleWebRTCOffer = data => {\n      if (data.senderId !== socket.id) {\n        handleOffer(data.offer);\n      }\n    };\n    const handleWebRTCAnswer = data => {\n      if (data.senderId !== socket.id) {\n        handleAnswer(data.answer);\n      }\n    };\n    const handleWebRTCICE = data => {\n      if (data.senderId !== socket.id) {\n        handleICECandidate(data.candidate);\n      }\n    };\n    socket.on('webrtc_offer', handleWebRTCOffer);\n    socket.on('webrtc_answer', handleWebRTCAnswer);\n    socket.on('webrtc_ice_candidate', handleWebRTCICE);\n    return () => {\n      socket.off('webrtc_offer', handleWebRTCOffer);\n      socket.off('webrtc_answer', handleWebRTCAnswer);\n      socket.off('webrtc_ice_candidate', handleWebRTCICE);\n    };\n  }, [socket, roomId, handleOffer, handleAnswer, handleICECandidate]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      endCall();\n    };\n  }, [endCall]);\n  return {\n    localStream,\n    remoteStream,\n    isCallActive,\n    isVideoEnabled,\n    isAudioEnabled,\n    callError,\n    connectionState,\n    localVideoRef,\n    remoteVideoRef,\n    startCall,\n    answerCall,\n    endCall,\n    toggleVideo,\n    toggleAudio,\n    handleOffer,\n    handleAnswer,\n    handleICECandidate\n  };\n};\n_s(useWebRTC, \"VAKKkM9FYj82PsOsRQv9nETx7oA=\", false, function () {\n  return [useSocket];\n});\nexport default useWebRTC;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "useSocket", "useWebRTC", "roomId", "isInitiator", "_s", "localStream", "setLocalStream", "remoteStream", "setRemoteStream", "isCallActive", "setIsCallActive", "isVideoEnabled", "setIsVideoEnabled", "isAudioEnabled", "setIsAudioEnabled", "callError", "setCallError", "connectionState", "setConnectionState", "localVideoRef", "remoteVideoRef", "peerConnectionRef", "localStreamRef", "socket", "sendWebRTCOffer", "sendWebRTCAnswer", "sendICECandidate", "iceServers", "urls", "initializePeerConnection", "current", "peerConnection", "RTCPeerConnection", "onicecandidate", "event", "candidate", "ontrack", "console", "log", "streams", "srcObject", "onconnectionstatechange", "oniceconnectionstatechange", "iceConnectionState", "getUserMedia", "video", "audio", "constraints", "width", "ideal", "height", "frameRate", "echoCancellation", "noiseSuppression", "autoGainControl", "stream", "navigator", "mediaDevices", "error", "startCall", "getTracks", "for<PERSON>ach", "track", "addTrack", "offer", "createOffer", "setLocalDescription", "answerCall", "handleOffer", "setRemoteDescription", "RTCSessionDescription", "answer", "createAnswer", "handleAnswer", "handleICECandidate", "addIceCandidate", "RTCIceCandidate", "toggleVideo", "videoTrack", "getVideoTracks", "enabled", "toggleAudio", "audioTrack", "getAudioTracks", "endCall", "stop", "close", "handleWebRTCOffer", "data", "senderId", "id", "handleWebRTCAnswer", "handleWebRTCICE", "on", "off"], "sources": ["E:/03/client/src/hooks/useWebRTC.js"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\nimport { useSocket } from '../context/SocketContext';\n\nconst useWebRTC = (roomId, isInitiator = false) => {\n  const [localStream, setLocalStream] = useState(null);\n  const [remoteStream, setRemoteStream] = useState(null);\n  const [isCallActive, setIsCallActive] = useState(false);\n  const [isVideoEnabled, setIsVideoEnabled] = useState(true);\n  const [isAudioEnabled, setIsAudioEnabled] = useState(true);\n  const [callError, setCallError] = useState(null);\n  const [connectionState, setConnectionState] = useState('new');\n\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n\n  const { socket, sendWebRTCOffer, sendWebRTCAnswer, sendICECandidate } = useSocket();\n\n  // ICE servers configuration\n  const iceServers = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n      { urls: 'stun:stun2.l.google.com:19302' }\n    ]\n  };\n\n  // Initialize peer connection\n  const initializePeerConnection = useCallback(() => {\n    if (peerConnectionRef.current) {\n      return peerConnectionRef.current;\n    }\n\n    const peerConnection = new RTCPeerConnection(iceServers);\n\n    // Handle ICE candidates\n    peerConnection.onicecandidate = (event) => {\n      if (event.candidate && roomId) {\n        sendICECandidate(roomId, event.candidate);\n      }\n    };\n\n    // Handle remote stream\n    peerConnection.ontrack = (event) => {\n      console.log('Received remote stream');\n      setRemoteStream(event.streams[0]);\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = event.streams[0];\n      }\n    };\n\n    // Handle connection state changes\n    peerConnection.onconnectionstatechange = () => {\n      setConnectionState(peerConnection.connectionState);\n      console.log('Connection state:', peerConnection.connectionState);\n      \n      if (peerConnection.connectionState === 'connected') {\n        setIsCallActive(true);\n        setCallError(null);\n      } else if (peerConnection.connectionState === 'failed') {\n        setCallError('Connection failed');\n        setIsCallActive(false);\n      } else if (peerConnection.connectionState === 'disconnected') {\n        setIsCallActive(false);\n      }\n    };\n\n    // Handle ICE connection state changes\n    peerConnection.oniceconnectionstatechange = () => {\n      console.log('ICE connection state:', peerConnection.iceConnectionState);\n      \n      if (peerConnection.iceConnectionState === 'failed') {\n        setCallError('ICE connection failed');\n      }\n    };\n\n    peerConnectionRef.current = peerConnection;\n    return peerConnection;\n  }, [roomId, sendICECandidate]);\n\n  // Get user media (camera and microphone)\n  const getUserMedia = useCallback(async (video = true, audio = true) => {\n    try {\n      const constraints = {\n        video: video ? {\n          width: { ideal: 1280 },\n          height: { ideal: 720 },\n          frameRate: { ideal: 30 }\n        } : false,\n        audio: audio ? {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        } : false\n      };\n\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      setLocalStream(stream);\n      localStreamRef.current = stream;\n\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n\n      return stream;\n    } catch (error) {\n      console.error('Error accessing media devices:', error);\n      setCallError('Could not access camera/microphone');\n      throw error;\n    }\n  }, []);\n\n  // Start call (for initiator)\n  const startCall = useCallback(async (video = true, audio = true) => {\n    try {\n      setCallError(null);\n      \n      // Get user media\n      const stream = await getUserMedia(video, audio);\n      \n      // Initialize peer connection\n      const peerConnection = initializePeerConnection();\n      \n      // Add local stream to peer connection\n      stream.getTracks().forEach(track => {\n        peerConnection.addTrack(track, stream);\n      });\n\n      // Create and send offer\n      const offer = await peerConnection.createOffer();\n      await peerConnection.setLocalDescription(offer);\n      \n      if (roomId) {\n        sendWebRTCOffer(roomId, offer);\n      }\n\n      setIsVideoEnabled(video);\n      setIsAudioEnabled(audio);\n      \n    } catch (error) {\n      console.error('Error starting call:', error);\n      setCallError('Failed to start call');\n    }\n  }, [getUserMedia, initializePeerConnection, roomId, sendWebRTCOffer]);\n\n  // Answer call (for receiver)\n  const answerCall = useCallback(async (video = true, audio = true) => {\n    try {\n      setCallError(null);\n      \n      // Get user media\n      const stream = await getUserMedia(video, audio);\n      \n      // Initialize peer connection\n      const peerConnection = initializePeerConnection();\n      \n      // Add local stream to peer connection\n      stream.getTracks().forEach(track => {\n        peerConnection.addTrack(track, stream);\n      });\n\n      setIsVideoEnabled(video);\n      setIsAudioEnabled(audio);\n      \n    } catch (error) {\n      console.error('Error answering call:', error);\n      setCallError('Failed to answer call');\n    }\n  }, [getUserMedia, initializePeerConnection]);\n\n  // Handle incoming offer\n  const handleOffer = useCallback(async (offer) => {\n    try {\n      const peerConnection = peerConnectionRef.current || initializePeerConnection();\n      \n      await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));\n      \n      const answer = await peerConnection.createAnswer();\n      await peerConnection.setLocalDescription(answer);\n      \n      if (roomId) {\n        sendWebRTCAnswer(roomId, answer);\n      }\n    } catch (error) {\n      console.error('Error handling offer:', error);\n      setCallError('Failed to handle incoming call');\n    }\n  }, [initializePeerConnection, roomId, sendWebRTCAnswer]);\n\n  // Handle incoming answer\n  const handleAnswer = useCallback(async (answer) => {\n    try {\n      const peerConnection = peerConnectionRef.current;\n      if (peerConnection) {\n        await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));\n      }\n    } catch (error) {\n      console.error('Error handling answer:', error);\n      setCallError('Failed to establish connection');\n    }\n  }, []);\n\n  // Handle incoming ICE candidate\n  const handleICECandidate = useCallback(async (candidate) => {\n    try {\n      const peerConnection = peerConnectionRef.current;\n      if (peerConnection) {\n        await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\n      }\n    } catch (error) {\n      console.error('Error handling ICE candidate:', error);\n    }\n  }, []);\n\n  // Toggle video\n  const toggleVideo = useCallback(() => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoEnabled(videoTrack.enabled);\n      }\n    }\n  }, []);\n\n  // Toggle audio\n  const toggleAudio = useCallback(() => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioEnabled(audioTrack.enabled);\n      }\n    }\n  }, []);\n\n  // End call\n  const endCall = useCallback(() => {\n    // Stop local stream\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => track.stop());\n    }\n\n    // Close peer connection\n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Reset states\n    setLocalStream(null);\n    setRemoteStream(null);\n    setIsCallActive(false);\n    setIsVideoEnabled(true);\n    setIsAudioEnabled(true);\n    setCallError(null);\n    setConnectionState('new');\n\n    // Clear video elements\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    localStreamRef.current = null;\n  }, []);\n\n  // Socket event listeners\n  useEffect(() => {\n    if (!socket || !roomId) return;\n\n    const handleWebRTCOffer = (data) => {\n      if (data.senderId !== socket.id) {\n        handleOffer(data.offer);\n      }\n    };\n\n    const handleWebRTCAnswer = (data) => {\n      if (data.senderId !== socket.id) {\n        handleAnswer(data.answer);\n      }\n    };\n\n    const handleWebRTCICE = (data) => {\n      if (data.senderId !== socket.id) {\n        handleICECandidate(data.candidate);\n      }\n    };\n\n    socket.on('webrtc_offer', handleWebRTCOffer);\n    socket.on('webrtc_answer', handleWebRTCAnswer);\n    socket.on('webrtc_ice_candidate', handleWebRTCICE);\n\n    return () => {\n      socket.off('webrtc_offer', handleWebRTCOffer);\n      socket.off('webrtc_answer', handleWebRTCAnswer);\n      socket.off('webrtc_ice_candidate', handleWebRTCICE);\n    };\n  }, [socket, roomId, handleOffer, handleAnswer, handleICECandidate]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      endCall();\n    };\n  }, [endCall]);\n\n  return {\n    localStream,\n    remoteStream,\n    isCallActive,\n    isVideoEnabled,\n    isAudioEnabled,\n    callError,\n    connectionState,\n    localVideoRef,\n    remoteVideoRef,\n    startCall,\n    answerCall,\n    endCall,\n    toggleVideo,\n    toggleAudio,\n    handleOffer,\n    handleAnswer,\n    handleICECandidate\n  };\n};\n\nexport default useWebRTC;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,MAAMC,SAAS,GAAGA,CAACC,MAAM,EAAEC,WAAW,GAAG,KAAK,KAAK;EAAAC,EAAA;EACjD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMuB,aAAa,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMsB,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuB,iBAAiB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMwB,cAAc,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM;IAAEyB,MAAM;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAiB,CAAC,GAAG1B,SAAS,CAAC,CAAC;;EAEnF;EACA,MAAM2B,UAAU,GAAG;IACjBA,UAAU,EAAE,CACV;MAAEC,IAAI,EAAE;IAA+B,CAAC,EACxC;MAAEA,IAAI,EAAE;IAAgC,CAAC,EACzC;MAAEA,IAAI,EAAE;IAAgC,CAAC;EAE7C,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAG9B,WAAW,CAAC,MAAM;IACjD,IAAIsB,iBAAiB,CAACS,OAAO,EAAE;MAC7B,OAAOT,iBAAiB,CAACS,OAAO;IAClC;IAEA,MAAMC,cAAc,GAAG,IAAIC,iBAAiB,CAACL,UAAU,CAAC;;IAExD;IACAI,cAAc,CAACE,cAAc,GAAIC,KAAK,IAAK;MACzC,IAAIA,KAAK,CAACC,SAAS,IAAIjC,MAAM,EAAE;QAC7BwB,gBAAgB,CAACxB,MAAM,EAAEgC,KAAK,CAACC,SAAS,CAAC;MAC3C;IACF,CAAC;;IAED;IACAJ,cAAc,CAACK,OAAO,GAAIF,KAAK,IAAK;MAClCG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC9B,eAAe,CAAC0B,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC,IAAInB,cAAc,CAACU,OAAO,EAAE;QAC1BV,cAAc,CAACU,OAAO,CAACU,SAAS,GAAGN,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;MACrD;IACF,CAAC;;IAED;IACAR,cAAc,CAACU,uBAAuB,GAAG,MAAM;MAC7CvB,kBAAkB,CAACa,cAAc,CAACd,eAAe,CAAC;MAClDoB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEP,cAAc,CAACd,eAAe,CAAC;MAEhE,IAAIc,cAAc,CAACd,eAAe,KAAK,WAAW,EAAE;QAClDP,eAAe,CAAC,IAAI,CAAC;QACrBM,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,MAAM,IAAIe,cAAc,CAACd,eAAe,KAAK,QAAQ,EAAE;QACtDD,YAAY,CAAC,mBAAmB,CAAC;QACjCN,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM,IAAIqB,cAAc,CAACd,eAAe,KAAK,cAAc,EAAE;QAC5DP,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;;IAED;IACAqB,cAAc,CAACW,0BAA0B,GAAG,MAAM;MAChDL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEP,cAAc,CAACY,kBAAkB,CAAC;MAEvE,IAAIZ,cAAc,CAACY,kBAAkB,KAAK,QAAQ,EAAE;QAClD3B,YAAY,CAAC,uBAAuB,CAAC;MACvC;IACF,CAAC;IAEDK,iBAAiB,CAACS,OAAO,GAAGC,cAAc;IAC1C,OAAOA,cAAc;EACvB,CAAC,EAAE,CAAC7B,MAAM,EAAEwB,gBAAgB,CAAC,CAAC;;EAE9B;EACA,MAAMkB,YAAY,GAAG7C,WAAW,CAAC,OAAO8C,KAAK,GAAG,IAAI,EAAEC,KAAK,GAAG,IAAI,KAAK;IACrE,IAAI;MACF,MAAMC,WAAW,GAAG;QAClBF,KAAK,EAAEA,KAAK,GAAG;UACbG,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC;UACtBC,MAAM,EAAE;YAAED,KAAK,EAAE;UAAI,CAAC;UACtBE,SAAS,EAAE;YAAEF,KAAK,EAAE;UAAG;QACzB,CAAC,GAAG,KAAK;QACTH,KAAK,EAAEA,KAAK,GAAG;UACbM,gBAAgB,EAAE,IAAI;UACtBC,gBAAgB,EAAE,IAAI;UACtBC,eAAe,EAAE;QACnB,CAAC,GAAG;MACN,CAAC;MAED,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACb,YAAY,CAACG,WAAW,CAAC;MACrEzC,cAAc,CAACiD,MAAM,CAAC;MACtBjC,cAAc,CAACQ,OAAO,GAAGyB,MAAM;MAE/B,IAAIpC,aAAa,CAACW,OAAO,EAAE;QACzBX,aAAa,CAACW,OAAO,CAACU,SAAS,GAAGe,MAAM;MAC1C;MAEA,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,YAAY,CAAC,oCAAoC,CAAC;MAClD,MAAM0C,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAG5D,WAAW,CAAC,OAAO8C,KAAK,GAAG,IAAI,EAAEC,KAAK,GAAG,IAAI,KAAK;IAClE,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMuC,MAAM,GAAG,MAAMX,YAAY,CAACC,KAAK,EAAEC,KAAK,CAAC;;MAE/C;MACA,MAAMf,cAAc,GAAGF,wBAAwB,CAAC,CAAC;;MAEjD;MACA0B,MAAM,CAACK,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC/B,cAAc,CAACgC,QAAQ,CAACD,KAAK,EAAEP,MAAM,CAAC;MACxC,CAAC,CAAC;;MAEF;MACA,MAAMS,KAAK,GAAG,MAAMjC,cAAc,CAACkC,WAAW,CAAC,CAAC;MAChD,MAAMlC,cAAc,CAACmC,mBAAmB,CAACF,KAAK,CAAC;MAE/C,IAAI9D,MAAM,EAAE;QACVsB,eAAe,CAACtB,MAAM,EAAE8D,KAAK,CAAC;MAChC;MAEApD,iBAAiB,CAACiC,KAAK,CAAC;MACxB/B,iBAAiB,CAACgC,KAAK,CAAC;IAE1B,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C1C,YAAY,CAAC,sBAAsB,CAAC;IACtC;EACF,CAAC,EAAE,CAAC4B,YAAY,EAAEf,wBAAwB,EAAE3B,MAAM,EAAEsB,eAAe,CAAC,CAAC;;EAErE;EACA,MAAM2C,UAAU,GAAGpE,WAAW,CAAC,OAAO8C,KAAK,GAAG,IAAI,EAAEC,KAAK,GAAG,IAAI,KAAK;IACnE,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMuC,MAAM,GAAG,MAAMX,YAAY,CAACC,KAAK,EAAEC,KAAK,CAAC;;MAE/C;MACA,MAAMf,cAAc,GAAGF,wBAAwB,CAAC,CAAC;;MAEjD;MACA0B,MAAM,CAACK,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC/B,cAAc,CAACgC,QAAQ,CAACD,KAAK,EAAEP,MAAM,CAAC;MACxC,CAAC,CAAC;MAEF3C,iBAAiB,CAACiC,KAAK,CAAC;MACxB/B,iBAAiB,CAACgC,KAAK,CAAC;IAE1B,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C1C,YAAY,CAAC,uBAAuB,CAAC;IACvC;EACF,CAAC,EAAE,CAAC4B,YAAY,EAAEf,wBAAwB,CAAC,CAAC;;EAE5C;EACA,MAAMuC,WAAW,GAAGrE,WAAW,CAAC,MAAOiE,KAAK,IAAK;IAC/C,IAAI;MACF,MAAMjC,cAAc,GAAGV,iBAAiB,CAACS,OAAO,IAAID,wBAAwB,CAAC,CAAC;MAE9E,MAAME,cAAc,CAACsC,oBAAoB,CAAC,IAAIC,qBAAqB,CAACN,KAAK,CAAC,CAAC;MAE3E,MAAMO,MAAM,GAAG,MAAMxC,cAAc,CAACyC,YAAY,CAAC,CAAC;MAClD,MAAMzC,cAAc,CAACmC,mBAAmB,CAACK,MAAM,CAAC;MAEhD,IAAIrE,MAAM,EAAE;QACVuB,gBAAgB,CAACvB,MAAM,EAAEqE,MAAM,CAAC;MAClC;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C1C,YAAY,CAAC,gCAAgC,CAAC;IAChD;EACF,CAAC,EAAE,CAACa,wBAAwB,EAAE3B,MAAM,EAAEuB,gBAAgB,CAAC,CAAC;;EAExD;EACA,MAAMgD,YAAY,GAAG1E,WAAW,CAAC,MAAOwE,MAAM,IAAK;IACjD,IAAI;MACF,MAAMxC,cAAc,GAAGV,iBAAiB,CAACS,OAAO;MAChD,IAAIC,cAAc,EAAE;QAClB,MAAMA,cAAc,CAACsC,oBAAoB,CAAC,IAAIC,qBAAqB,CAACC,MAAM,CAAC,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C1C,YAAY,CAAC,gCAAgC,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0D,kBAAkB,GAAG3E,WAAW,CAAC,MAAOoC,SAAS,IAAK;IAC1D,IAAI;MACF,MAAMJ,cAAc,GAAGV,iBAAiB,CAACS,OAAO;MAChD,IAAIC,cAAc,EAAE;QAClB,MAAMA,cAAc,CAAC4C,eAAe,CAAC,IAAIC,eAAe,CAACzC,SAAS,CAAC,CAAC;MACtE;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmB,WAAW,GAAG9E,WAAW,CAAC,MAAM;IACpC,IAAIuB,cAAc,CAACQ,OAAO,EAAE;MAC1B,MAAMgD,UAAU,GAAGxD,cAAc,CAACQ,OAAO,CAACiD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;QACxCpE,iBAAiB,CAACkE,UAAU,CAACE,OAAO,CAAC;MACvC;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAGlF,WAAW,CAAC,MAAM;IACpC,IAAIuB,cAAc,CAACQ,OAAO,EAAE;MAC1B,MAAMoD,UAAU,GAAG5D,cAAc,CAACQ,OAAO,CAACqD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;QACxClE,iBAAiB,CAACoE,UAAU,CAACF,OAAO,CAAC;MACvC;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,OAAO,GAAGrF,WAAW,CAAC,MAAM;IAChC;IACA,IAAIuB,cAAc,CAACQ,OAAO,EAAE;MAC1BR,cAAc,CAACQ,OAAO,CAAC8B,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACuB,IAAI,CAAC,CAAC,CAAC;IACnE;;IAEA;IACA,IAAIhE,iBAAiB,CAACS,OAAO,EAAE;MAC7BT,iBAAiB,CAACS,OAAO,CAACwD,KAAK,CAAC,CAAC;MACjCjE,iBAAiB,CAACS,OAAO,GAAG,IAAI;IAClC;;IAEA;IACAxB,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;IACtBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,YAAY,CAAC,IAAI,CAAC;IAClBE,kBAAkB,CAAC,KAAK,CAAC;;IAEzB;IACA,IAAIC,aAAa,CAACW,OAAO,EAAE;MACzBX,aAAa,CAACW,OAAO,CAACU,SAAS,GAAG,IAAI;IACxC;IACA,IAAIpB,cAAc,CAACU,OAAO,EAAE;MAC1BV,cAAc,CAACU,OAAO,CAACU,SAAS,GAAG,IAAI;IACzC;IAEAlB,cAAc,CAACQ,OAAO,GAAG,IAAI;EAC/B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0B,MAAM,IAAI,CAACrB,MAAM,EAAE;IAExB,MAAMqF,iBAAiB,GAAIC,IAAI,IAAK;MAClC,IAAIA,IAAI,CAACC,QAAQ,KAAKlE,MAAM,CAACmE,EAAE,EAAE;QAC/BtB,WAAW,CAACoB,IAAI,CAACxB,KAAK,CAAC;MACzB;IACF,CAAC;IAED,MAAM2B,kBAAkB,GAAIH,IAAI,IAAK;MACnC,IAAIA,IAAI,CAACC,QAAQ,KAAKlE,MAAM,CAACmE,EAAE,EAAE;QAC/BjB,YAAY,CAACe,IAAI,CAACjB,MAAM,CAAC;MAC3B;IACF,CAAC;IAED,MAAMqB,eAAe,GAAIJ,IAAI,IAAK;MAChC,IAAIA,IAAI,CAACC,QAAQ,KAAKlE,MAAM,CAACmE,EAAE,EAAE;QAC/BhB,kBAAkB,CAACc,IAAI,CAACrD,SAAS,CAAC;MACpC;IACF,CAAC;IAEDZ,MAAM,CAACsE,EAAE,CAAC,cAAc,EAAEN,iBAAiB,CAAC;IAC5ChE,MAAM,CAACsE,EAAE,CAAC,eAAe,EAAEF,kBAAkB,CAAC;IAC9CpE,MAAM,CAACsE,EAAE,CAAC,sBAAsB,EAAED,eAAe,CAAC;IAElD,OAAO,MAAM;MACXrE,MAAM,CAACuE,GAAG,CAAC,cAAc,EAAEP,iBAAiB,CAAC;MAC7ChE,MAAM,CAACuE,GAAG,CAAC,eAAe,EAAEH,kBAAkB,CAAC;MAC/CpE,MAAM,CAACuE,GAAG,CAAC,sBAAsB,EAAEF,eAAe,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CAACrE,MAAM,EAAErB,MAAM,EAAEkE,WAAW,EAAEK,YAAY,EAAEC,kBAAkB,CAAC,CAAC;;EAEnE;EACA7E,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXuF,OAAO,CAAC,CAAC;IACX,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,OAAO;IACL/E,WAAW;IACXE,YAAY;IACZE,YAAY;IACZE,cAAc;IACdE,cAAc;IACdE,SAAS;IACTE,eAAe;IACfE,aAAa;IACbC,cAAc;IACduC,SAAS;IACTQ,UAAU;IACViB,OAAO;IACPP,WAAW;IACXI,WAAW;IACXb,WAAW;IACXK,YAAY;IACZC;EACF,CAAC;AACH,CAAC;AAACtE,EAAA,CAtUIH,SAAS;EAAA,QAc2DD,SAAS;AAAA;AA0TnF,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}