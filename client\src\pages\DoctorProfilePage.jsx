import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { doctorAPI } from '../services/api';
import BookingForm from '../components/BookingForm';

const DoctorProfilePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [doctor, setDoctor] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [bookingSuccess, setBookingSuccess] = useState(false);

  useEffect(() => {
    fetchDoctorProfile();
  }, [id]);

  const fetchDoctorProfile = async () => {
    try {
      setLoading(true);
      const response = await doctorAPI.getDoctorById(id);
      setDoctor(response.data.data.doctor);
    } catch (error) {
      setError('Doctor not found or not available');
      console.error('Error fetching doctor:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookingSuccess = () => {
    setBookingSuccess(true);
    setTimeout(() => {
      setBookingSuccess(false);
    }, 5000);
  };

  const formatAvailability = (availability) => {
    return availability
      .filter(day => day.isAvailable)
      .map(day => `${day.day}: ${day.startTime} - ${day.endTime}`)
      .join(', ');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !doctor) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😞</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Doctor Not Found</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => navigate('/')}
            className="btn-primary"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Message */}
        {bookingSuccess && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-green-600 mr-3">✅</div>
              <div>
                <h4 className="text-green-800 font-semibold">Appointment Booked Successfully!</h4>
                <p className="text-green-700 text-sm">
                  Your appointment has been booked. You can view it in your dashboard.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Doctor Information */}
          <div className="lg:col-span-2">
            <div className="card">
              {/* Doctor Header */}
              <div className="flex items-start space-x-6 mb-6">
                <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                  {doctor.profileImage ? (
                    <img 
                      src={doctor.profileImage} 
                      alt={doctor.user.name}
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-3xl font-semibold text-gray-600">
                      {doctor.user.name.charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>

                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    Dr. {doctor.user.name}
                  </h1>
                  
                  <div className="flex items-center space-x-4 mb-3">
                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                      {doctor.specialty}
                    </span>
                    <span className="text-gray-600">
                      {doctor.experienceInYears} years experience
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-green-600 font-medium">Available for appointments</span>
                  </div>

                  <div className="text-2xl font-bold text-gray-900">
                    ${doctor.consultationFee}
                    <span className="text-base font-normal text-gray-600 ml-1">
                      consultation fee
                    </span>
                  </div>
                </div>
              </div>

              {/* Qualifications */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Qualifications</h3>
                <p className="text-gray-700 leading-relaxed">
                  {doctor.qualifications}
                </p>
              </div>

              {/* About */}
              {doctor.about && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">About</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {doctor.about}
                  </p>
                </div>
              )}

              {/* Availability */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Availability</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {doctor.availability.map((day) => (
                    <div 
                      key={day.day}
                      className={`p-3 rounded-lg border ${
                        day.isAvailable 
                          ? 'bg-green-50 border-green-200' 
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">{day.day}</span>
                        {day.isAvailable ? (
                          <span className="text-green-600 text-sm">
                            {day.startTime} - {day.endTime}
                          </span>
                        ) : (
                          <span className="text-gray-500 text-sm">Unavailable</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Contact Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-gray-500">📧</span>
                    <span className="text-gray-700">{doctor.user.email}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Form */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <BookingForm 
                doctorId={doctor.user._id} 
                onBookingSuccess={handleBookingSuccess}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorProfilePage;
