[{"E:\\03\\client\\src\\index.js": "1", "E:\\03\\client\\src\\App.js": "2", "E:\\03\\client\\src\\context\\AuthContext.jsx": "3", "E:\\03\\client\\src\\components\\ProtectedRoute.jsx": "4", "E:\\03\\client\\src\\components\\Navbar.jsx": "5", "E:\\03\\client\\src\\pages\\HomePage.jsx": "6", "E:\\03\\client\\src\\pages\\LoginPage.jsx": "7", "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx": "8", "E:\\03\\client\\src\\pages\\PatientDashboard.jsx": "9", "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx": "10", "E:\\03\\client\\src\\pages\\RegisterPage.jsx": "11", "E:\\03\\client\\src\\services\\api.js": "12", "E:\\03\\client\\src\\components\\DoctorCard.jsx": "13", "E:\\03\\client\\src\\components\\BookingForm.jsx": "14", "E:\\03\\client\\src\\context\\SocketContext.jsx": "15", "E:\\03\\client\\src\\components\\NotificationCenter.jsx": "16", "E:\\03\\client\\src\\components\\HelplineSupport.jsx": "17", "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx": "18", "E:\\03\\client\\src\\components\\VideoCall.jsx": "19", "E:\\03\\client\\src\\components\\ChatInterface.jsx": "20", "E:\\03\\client\\src\\hooks\\useWebRTC.js": "21"}, {"size": 254, "mtime": 1751819478486, "results": "22", "hashOfConfig": "23"}, {"size": 2532, "mtime": 1751825143894, "results": "24", "hashOfConfig": "23"}, {"size": 5478, "mtime": 1751819156379, "results": "25", "hashOfConfig": "23"}, {"size": 838, "mtime": 1751819460972, "results": "26", "hashOfConfig": "23"}, {"size": 5371, "mtime": 1751825201603, "results": "27", "hashOfConfig": "23"}, {"size": 7108, "mtime": 1751819217590, "results": "28", "hashOfConfig": "23"}, {"size": 4967, "mtime": 1751819236976, "results": "29", "hashOfConfig": "23"}, {"size": 10230, "mtime": 1751825273427, "results": "30", "hashOfConfig": "23"}, {"size": 10651, "mtime": 1751819402216, "results": "31", "hashOfConfig": "23"}, {"size": 13478, "mtime": 1751819445479, "results": "32", "hashOfConfig": "23"}, {"size": 14901, "mtime": 1751819279719, "results": "33", "hashOfConfig": "23"}, {"size": 3711, "mtime": 1751824870172, "results": "34", "hashOfConfig": "23"}, {"size": 2556, "mtime": 1751819190739, "results": "35", "hashOfConfig": "23"}, {"size": 6520, "mtime": 1751819328878, "results": "36", "hashOfConfig": "23"}, {"size": 8609, "mtime": 1751824813793, "results": "37", "hashOfConfig": "23"}, {"size": 6777, "mtime": 1751825065593, "results": "38", "hashOfConfig": "23"}, {"size": 16683, "mtime": 1751825033036, "results": "39", "hashOfConfig": "23"}, {"size": 12534, "mtime": 1751825111199, "results": "40", "hashOfConfig": "23"}, {"size": 7781, "mtime": 1751824914817, "results": "41", "hashOfConfig": "23"}, {"size": 10283, "mtime": 1751824954856, "results": "42", "hashOfConfig": "23"}, {"size": 9622, "mtime": 1751824852519, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1syuwl8", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\03\\client\\src\\index.js", [], [], "E:\\03\\client\\src\\App.js", [], [], "E:\\03\\client\\src\\context\\AuthContext.jsx", [], [], "E:\\03\\client\\src\\components\\ProtectedRoute.jsx", [], [], "E:\\03\\client\\src\\components\\Navbar.jsx", ["107"], [], "E:\\03\\client\\src\\pages\\HomePage.jsx", ["108"], [], "E:\\03\\client\\src\\pages\\LoginPage.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx", ["109", "110"], [], "E:\\03\\client\\src\\pages\\PatientDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\RegisterPage.jsx", [], [], "E:\\03\\client\\src\\services\\api.js", [], [], "E:\\03\\client\\src\\components\\DoctorCard.jsx", [], [], "E:\\03\\client\\src\\components\\BookingForm.jsx", ["111"], [], "E:\\03\\client\\src\\context\\SocketContext.jsx", ["112"], [], "E:\\03\\client\\src\\components\\NotificationCenter.jsx", [], [], "E:\\03\\client\\src\\components\\HelplineSupport.jsx", ["113", "114"], [], "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx", ["115"], [], "E:\\03\\client\\src\\components\\VideoCall.jsx", ["116"], [], "E:\\03\\client\\src\\components\\ChatInterface.jsx", ["117"], [], "E:\\03\\client\\src\\hooks\\useWebRTC.js", ["118"], [], {"ruleId": "119", "severity": 1, "message": "120", "line": 3, "column": 27, "nodeType": "121", "messageId": "122", "endLine": 3, "endColumn": 34}, {"ruleId": "123", "severity": 1, "message": "124", "line": 16, "column": 6, "nodeType": "125", "endLine": 16, "endColumn": 25, "suggestions": "126"}, {"ruleId": "123", "severity": 1, "message": "127", "line": 24, "column": 6, "nodeType": "125", "endLine": 24, "endColumn": 10, "suggestions": "128"}, {"ruleId": "119", "severity": 1, "message": "129", "line": 46, "column": 9, "nodeType": "121", "messageId": "122", "endLine": 46, "endColumn": 27}, {"ruleId": "123", "severity": 1, "message": "130", "line": 23, "column": 6, "nodeType": "125", "endLine": 23, "endColumn": 30, "suggestions": "131"}, {"ruleId": "123", "severity": 1, "message": "132", "line": 170, "column": 6, "nodeType": "125", "endLine": 170, "endColumn": 36, "suggestions": "133"}, {"ruleId": "123", "severity": 1, "message": "134", "line": 33, "column": 6, "nodeType": "125", "endLine": 33, "endColumn": 22, "suggestions": "135"}, {"ruleId": "119", "severity": 1, "message": "136", "line": 114, "column": 13, "nodeType": "121", "messageId": "122", "endLine": 114, "endColumn": 21}, {"ruleId": "119", "severity": 1, "message": "137", "line": 19, "column": 10, "nodeType": "121", "messageId": "122", "endLine": 19, "endColumn": 15}, {"ruleId": "119", "severity": 1, "message": "138", "line": 14, "column": 5, "nodeType": "121", "messageId": "122", "endLine": 14, "endColumn": 16}, {"ruleId": "123", "severity": 1, "message": "139", "line": 34, "column": 6, "nodeType": "125", "endLine": 34, "endColumn": 18, "suggestions": "140"}, {"ruleId": "123", "severity": 1, "message": "141", "line": 80, "column": 6, "nodeType": "125", "endLine": 80, "endColumn": 32, "suggestions": "142"}, "no-unused-vars", "'FiPhone' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDoctors'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], "React Hook useEffect has a missing dependency: 'fetchDoctorProfile'. Either include it or remove the dependency array.", ["144"], "'formatAvailability' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailableSlots'. Either include it or remove the dependency array.", ["145"], "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", ["146"], "React Hook useEffect has a missing dependency: 'joinHelpline'. Either include it or remove the dependency array.", ["147"], "'response' is assigned a value but never used.", "'error' is assigned a value but never used.", "'localStream' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchMessages' and 'joinChat'. Either include them or remove the dependency array.", ["148"], "React Hook useCallback has a missing dependency: 'iceServers'. Either include it or remove the dependency array.", ["149"], {"desc": "150", "fix": "151"}, {"desc": "152", "fix": "153"}, {"desc": "154", "fix": "155"}, {"desc": "156", "fix": "157"}, {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, "Update the dependencies array to be: [fetchDoctors, selectedSpecialty]", {"range": "164", "text": "165"}, "Update the dependencies array to be: [fetchDoctorProfile, id]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [selectedDate, doctorId, fetchAvailableSlots]", {"range": "168", "text": "169"}, "Update the dependencies array to be: [addNotification, isAuthenticated, token, user]", {"range": "170", "text": "171"}, "Update the dependencies array to be: [isOpen, joinHelpline, socket]", {"range": "172", "text": "173"}, "Update the dependencies array to be: [fetchMessages, joinChat, receiverId]", {"range": "174", "text": "175"}, "Update the dependencies array to be: [iceServers, roomId, sendICECandidate]", {"range": "176", "text": "177"}, [552, 571], "[fetchDoctors, selectedSpecialty]", [965, 969], "[fetchDoctorProfile, id]", [820, 844], "[selectedDate, doctorId, fetchAvailableSlots]", [5041, 5071], "[addNotification, isAuthenticated, token, user]", [1170, 1186], "[isOpen, joinHelpline, socket]", [1048, 1060], "[fetchMessages, joinChat, receiverId]", [2745, 2771], "[iceServers, roomId, sendICECandidate]"]