[{"E:\\03\\client\\src\\index.js": "1", "E:\\03\\client\\src\\App.js": "2", "E:\\03\\client\\src\\context\\AuthContext.jsx": "3", "E:\\03\\client\\src\\components\\ProtectedRoute.jsx": "4", "E:\\03\\client\\src\\components\\Navbar.jsx": "5", "E:\\03\\client\\src\\pages\\HomePage.jsx": "6", "E:\\03\\client\\src\\pages\\LoginPage.jsx": "7", "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx": "8", "E:\\03\\client\\src\\pages\\PatientDashboard.jsx": "9", "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx": "10", "E:\\03\\client\\src\\pages\\RegisterPage.jsx": "11", "E:\\03\\client\\src\\services\\api.js": "12", "E:\\03\\client\\src\\components\\DoctorCard.jsx": "13", "E:\\03\\client\\src\\components\\BookingForm.jsx": "14", "E:\\03\\client\\src\\context\\SocketContext.jsx": "15", "E:\\03\\client\\src\\components\\NotificationCenter.jsx": "16", "E:\\03\\client\\src\\components\\HelplineSupport.jsx": "17", "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx": "18", "E:\\03\\client\\src\\components\\VideoCall.jsx": "19", "E:\\03\\client\\src\\components\\ChatInterface.jsx": "20", "E:\\03\\client\\src\\hooks\\useWebRTC.js": "21", "E:\\03\\client\\src\\components\\CommunicationTest.jsx": "22"}, {"size": 254, "mtime": 1751819478486, "results": "23", "hashOfConfig": "24"}, {"size": 2677, "mtime": 1751826572983, "results": "25", "hashOfConfig": "24"}, {"size": 5478, "mtime": 1751819156379, "results": "26", "hashOfConfig": "24"}, {"size": 838, "mtime": 1751819460972, "results": "27", "hashOfConfig": "24"}, {"size": 5371, "mtime": 1751826276137, "results": "28", "hashOfConfig": "24"}, {"size": 7108, "mtime": 1751819217590, "results": "29", "hashOfConfig": "24"}, {"size": 4967, "mtime": 1751819236976, "results": "30", "hashOfConfig": "24"}, {"size": 10230, "mtime": 1751825273427, "results": "31", "hashOfConfig": "24"}, {"size": 10651, "mtime": 1751819402216, "results": "32", "hashOfConfig": "24"}, {"size": 13478, "mtime": 1751819445479, "results": "33", "hashOfConfig": "24"}, {"size": 14901, "mtime": 1751819279719, "results": "34", "hashOfConfig": "24"}, {"size": 3711, "mtime": 1751824870172, "results": "35", "hashOfConfig": "24"}, {"size": 2556, "mtime": 1751819190739, "results": "36", "hashOfConfig": "24"}, {"size": 6520, "mtime": 1751819328878, "results": "37", "hashOfConfig": "24"}, {"size": 8635, "mtime": 1751826274535, "results": "38", "hashOfConfig": "24"}, {"size": 6777, "mtime": 1751825065593, "results": "39", "hashOfConfig": "24"}, {"size": 16688, "mtime": 1751826403718, "results": "40", "hashOfConfig": "24"}, {"size": 12535, "mtime": 1751826503309, "results": "41", "hashOfConfig": "24"}, {"size": 7781, "mtime": 1751824914817, "results": "42", "hashOfConfig": "24"}, {"size": 10289, "mtime": 1751826484859, "results": "43", "hashOfConfig": "24"}, {"size": 9622, "mtime": 1751824852519, "results": "44", "hashOfConfig": "24"}, {"size": 7140, "mtime": 1751826548655, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1syuwl8", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\03\\client\\src\\index.js", [], [], "E:\\03\\client\\src\\App.js", [], [], "E:\\03\\client\\src\\context\\AuthContext.jsx", [], [], "E:\\03\\client\\src\\components\\ProtectedRoute.jsx", [], [], "E:\\03\\client\\src\\components\\Navbar.jsx", ["112"], [], "E:\\03\\client\\src\\pages\\HomePage.jsx", ["113"], [], "E:\\03\\client\\src\\pages\\LoginPage.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx", ["114", "115"], [], "E:\\03\\client\\src\\pages\\PatientDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\RegisterPage.jsx", [], [], "E:\\03\\client\\src\\services\\api.js", [], [], "E:\\03\\client\\src\\components\\DoctorCard.jsx", [], [], "E:\\03\\client\\src\\components\\BookingForm.jsx", ["116"], [], "E:\\03\\client\\src\\context\\SocketContext.jsx", ["117"], [], "E:\\03\\client\\src\\components\\NotificationCenter.jsx", [], [], "E:\\03\\client\\src\\components\\HelplineSupport.jsx", ["118", "119"], [], "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx", ["120"], [], "E:\\03\\client\\src\\components\\VideoCall.jsx", ["121"], [], "E:\\03\\client\\src\\components\\ChatInterface.jsx", ["122"], [], "E:\\03\\client\\src\\hooks\\useWebRTC.js", ["123"], [], "E:\\03\\client\\src\\components\\CommunicationTest.jsx", [], [], {"ruleId": "124", "severity": 1, "message": "125", "line": 3, "column": 27, "nodeType": "126", "messageId": "127", "endLine": 3, "endColumn": 34}, {"ruleId": "128", "severity": 1, "message": "129", "line": 16, "column": 6, "nodeType": "130", "endLine": 16, "endColumn": 25, "suggestions": "131"}, {"ruleId": "128", "severity": 1, "message": "132", "line": 24, "column": 6, "nodeType": "130", "endLine": 24, "endColumn": 10, "suggestions": "133"}, {"ruleId": "124", "severity": 1, "message": "134", "line": 46, "column": 9, "nodeType": "126", "messageId": "127", "endLine": 46, "endColumn": 27}, {"ruleId": "128", "severity": 1, "message": "135", "line": 23, "column": 6, "nodeType": "130", "endLine": 23, "endColumn": 30, "suggestions": "136"}, {"ruleId": "128", "severity": 1, "message": "137", "line": 171, "column": 6, "nodeType": "130", "endLine": 171, "endColumn": 36, "suggestions": "138"}, {"ruleId": "128", "severity": 1, "message": "139", "line": 33, "column": 6, "nodeType": "130", "endLine": 33, "endColumn": 22, "suggestions": "140"}, {"ruleId": "124", "severity": 1, "message": "141", "line": 114, "column": 13, "nodeType": "126", "messageId": "127", "endLine": 114, "endColumn": 21}, {"ruleId": "124", "severity": 1, "message": "142", "line": 19, "column": 10, "nodeType": "126", "messageId": "127", "endLine": 19, "endColumn": 15}, {"ruleId": "124", "severity": 1, "message": "143", "line": 14, "column": 5, "nodeType": "126", "messageId": "127", "endLine": 14, "endColumn": 16}, {"ruleId": "128", "severity": 1, "message": "144", "line": 34, "column": 6, "nodeType": "130", "endLine": 34, "endColumn": 18, "suggestions": "145"}, {"ruleId": "128", "severity": 1, "message": "146", "line": 80, "column": 6, "nodeType": "130", "endLine": 80, "endColumn": 32, "suggestions": "147"}, "no-unused-vars", "'FiPhone' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDoctors'. Either include it or remove the dependency array.", "ArrayExpression", ["148"], "React Hook useEffect has a missing dependency: 'fetchDoctorProfile'. Either include it or remove the dependency array.", ["149"], "'formatAvailability' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailableSlots'. Either include it or remove the dependency array.", ["150"], "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", ["151"], "React Hook useEffect has a missing dependency: 'joinHelpline'. Either include it or remove the dependency array.", ["152"], "'response' is assigned a value but never used.", "'error' is assigned a value but never used.", "'localStream' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchMessages' and 'joinChat'. Either include them or remove the dependency array.", ["153"], "React Hook useCallback has a missing dependency: 'iceServers'. Either include it or remove the dependency array.", ["154"], {"desc": "155", "fix": "156"}, {"desc": "157", "fix": "158"}, {"desc": "159", "fix": "160"}, {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, "Update the dependencies array to be: [fetchDoctors, selectedSpecialty]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [fetchDoctorProfile, id]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [selectedDate, doctorId, fetchAvailableSlots]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [addNotification, isAuthenticated, token, user]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [isOpen, joinHelpline, socket]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [fetchMessages, joinChat, receiverId]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [iceServers, roomId, sendICECandidate]", {"range": "181", "text": "182"}, [552, 571], "[fetchDoctors, selectedSpecialty]", [965, 969], "[fetchDoctorProfile, id]", [820, 844], "[selectedDate, doctorId, fetchAvailableSlots]", [5067, 5097], "[addNotification, isAuthenticated, token, user]", [1170, 1186], "[isOpen, joinHelpline, socket]", [1048, 1060], "[fetchMessages, joinChat, receiverId]", [2745, 2771], "[iceServers, roomId, sendICECandidate]"]