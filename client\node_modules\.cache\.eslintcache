[{"E:\\03\\client\\src\\index.js": "1", "E:\\03\\client\\src\\App.js": "2", "E:\\03\\client\\src\\context\\AuthContext.jsx": "3", "E:\\03\\client\\src\\components\\ProtectedRoute.jsx": "4", "E:\\03\\client\\src\\components\\Navbar.jsx": "5", "E:\\03\\client\\src\\pages\\HomePage.jsx": "6", "E:\\03\\client\\src\\pages\\LoginPage.jsx": "7", "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx": "8", "E:\\03\\client\\src\\pages\\PatientDashboard.jsx": "9", "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx": "10", "E:\\03\\client\\src\\pages\\RegisterPage.jsx": "11", "E:\\03\\client\\src\\services\\api.js": "12", "E:\\03\\client\\src\\components\\DoctorCard.jsx": "13", "E:\\03\\client\\src\\components\\BookingForm.jsx": "14"}, {"size": 254, "mtime": 1751819478486, "results": "15", "hashOfConfig": "16"}, {"size": 2318, "mtime": 1751819472483, "results": "17", "hashOfConfig": "16"}, {"size": 5478, "mtime": 1751819156379, "results": "18", "hashOfConfig": "16"}, {"size": 838, "mtime": 1751819460972, "results": "19", "hashOfConfig": "16"}, {"size": 3001, "mtime": 1751819177151, "results": "20", "hashOfConfig": "16"}, {"size": 7108, "mtime": 1751819217590, "results": "21", "hashOfConfig": "16"}, {"size": 4967, "mtime": 1751819236976, "results": "22", "hashOfConfig": "16"}, {"size": 7709, "mtime": 1751819358030, "results": "23", "hashOfConfig": "16"}, {"size": 10651, "mtime": 1751819402216, "results": "24", "hashOfConfig": "16"}, {"size": 13478, "mtime": 1751819445479, "results": "25", "hashOfConfig": "16"}, {"size": 14901, "mtime": 1751819279719, "results": "26", "hashOfConfig": "16"}, {"size": 2140, "mtime": 1751819134746, "results": "27", "hashOfConfig": "16"}, {"size": 2556, "mtime": 1751819190739, "results": "28", "hashOfConfig": "16"}, {"size": 6520, "mtime": 1751819328878, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1syuwl8", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\03\\client\\src\\index.js", [], [], "E:\\03\\client\\src\\App.js", [], [], "E:\\03\\client\\src\\context\\AuthContext.jsx", [], [], "E:\\03\\client\\src\\components\\ProtectedRoute.jsx", [], [], "E:\\03\\client\\src\\components\\Navbar.jsx", [], [], "E:\\03\\client\\src\\pages\\HomePage.jsx", ["72"], [], "E:\\03\\client\\src\\pages\\LoginPage.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx", ["73", "74"], [], "E:\\03\\client\\src\\pages\\PatientDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\RegisterPage.jsx", [], [], "E:\\03\\client\\src\\services\\api.js", [], [], "E:\\03\\client\\src\\components\\DoctorCard.jsx", [], [], "E:\\03\\client\\src\\components\\BookingForm.jsx", ["75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 16, "column": 6, "nodeType": "78", "endLine": 16, "endColumn": 25, "suggestions": "79"}, {"ruleId": "76", "severity": 1, "message": "80", "line": 16, "column": 6, "nodeType": "78", "endLine": 16, "endColumn": 10, "suggestions": "81"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 38, "column": 9, "nodeType": "84", "messageId": "85", "endLine": 38, "endColumn": 27}, {"ruleId": "76", "severity": 1, "message": "86", "line": 23, "column": 6, "nodeType": "78", "endLine": 23, "endColumn": 30, "suggestions": "87"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDoctors'. Either include it or remove the dependency array.", "ArrayExpression", ["88"], "React Hook useEffect has a missing dependency: 'fetchDoctorProfile'. Either include it or remove the dependency array.", ["89"], "no-unused-vars", "'formatAvailability' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchAvailableSlots'. Either include it or remove the dependency array.", ["90"], {"desc": "91", "fix": "92"}, {"desc": "93", "fix": "94"}, {"desc": "95", "fix": "96"}, "Update the dependencies array to be: [fetchDoctors, selectedSpecialty]", {"range": "97", "text": "98"}, "Update the dependencies array to be: [fetchDoctorProfile, id]", {"range": "99", "text": "100"}, "Update the dependencies array to be: [selectedDate, doctorId, fetchAvailableSlots]", {"range": "101", "text": "102"}, [552, 571], "[fetchDoctors, selectedSpecialty]", [559, 563], "[fetchDoctorProfile, id]", [820, 844], "[selectedDate, doctorId, fetchAvailableSlots]"]