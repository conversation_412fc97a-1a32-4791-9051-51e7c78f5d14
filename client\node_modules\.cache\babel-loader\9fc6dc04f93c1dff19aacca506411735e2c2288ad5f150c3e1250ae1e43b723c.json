{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\pages\\\\RegisterPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { doctorAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RegisterPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'patient',\n    // Doctor specific fields\n    specialty: '',\n    qualifications: '',\n    experienceInYears: '',\n    consultationFee: ''\n  });\n  const [specialties, setSpecialties] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [validationErrors, setValidationErrors] = useState({});\n  const {\n    register,\n    isAuthenticated,\n    error,\n    clearError\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  // Fetch specialties for doctors\n  useEffect(() => {\n    const fetchSpecialties = async () => {\n      try {\n        const response = await doctorAPI.getSpecialties();\n        setSpecialties(response.data.data.specialties);\n      } catch (error) {\n        console.error('Error fetching specialties:', error);\n      }\n    };\n    fetchSpecialties();\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Clear validation error for this field\n    if (validationErrors[name]) {\n      setValidationErrors({\n        ...validationErrors,\n        [name]: ''\n      });\n    }\n  };\n  const validateForm = () => {\n    const errors = {};\n\n    // Basic validation\n    if (!formData.name.trim()) errors.name = 'Name is required';\n    if (!formData.email.trim()) errors.email = 'Email is required';\n    if (!formData.password) errors.password = 'Password is required';\n    if (formData.password.length < 6) errors.password = 'Password must be at least 6 characters';\n    if (formData.password !== formData.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n\n    // Doctor specific validation\n    if (formData.role === 'doctor') {\n      if (!formData.specialty) errors.specialty = 'Specialty is required';\n      if (!formData.qualifications.trim()) errors.qualifications = 'Qualifications are required';\n      if (!formData.experienceInYears) errors.experienceInYears = 'Experience is required';\n      if (!formData.consultationFee) errors.consultationFee = 'Consultation fee is required';\n      if (formData.experienceInYears && (formData.experienceInYears < 0 || formData.experienceInYears > 50)) {\n        errors.experienceInYears = 'Experience must be between 0 and 50 years';\n      }\n      if (formData.consultationFee && formData.consultationFee < 0) {\n        errors.consultationFee = 'Consultation fee cannot be negative';\n      }\n    }\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n\n    // Prepare data for submission\n    const submitData = {\n      name: formData.name.trim(),\n      email: formData.email.trim(),\n      password: formData.password,\n      role: formData.role\n    };\n\n    // Add doctor specific fields\n    if (formData.role === 'doctor') {\n      submitData.specialty = formData.specialty;\n      submitData.qualifications = formData.qualifications.trim();\n      submitData.experienceInYears = parseInt(formData.experienceInYears);\n      submitData.consultationFee = parseFloat(formData.consultationFee);\n    }\n    const result = await register(submitData);\n    if (result.success) {\n      navigate('/');\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-bold text-xl\",\n            children: \"D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-gray-600\",\n          children: \"Join DocBook to manage your healthcare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-800 text-sm\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"I am a\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"role\",\n                  value: \"patient\",\n                  checked: formData.role === 'patient',\n                  onChange: handleChange,\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), \"Patient\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"role\",\n                  value: \"doctor\",\n                  checked: formData.role === 'doctor',\n                  onChange: handleChange,\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), \"Doctor\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"name\",\n              name: \"name\",\n              type: \"text\",\n              required: true,\n              value: formData.name,\n              onChange: handleChange,\n              className: `input-field ${validationErrors.name ? 'border-red-500' : ''}`,\n              placeholder: \"Enter your full name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), validationErrors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: validationErrors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              required: true,\n              value: formData.email,\n              onChange: handleChange,\n              className: `input-field ${validationErrors.email ? 'border-red-500' : ''}`,\n              placeholder: \"Enter your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), validationErrors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: validationErrors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              required: true,\n              value: formData.password,\n              onChange: handleChange,\n              className: `input-field ${validationErrors.password ? 'border-red-500' : ''}`,\n              placeholder: \"Enter your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), validationErrors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: validationErrors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"confirmPassword\",\n              name: \"confirmPassword\",\n              type: \"password\",\n              required: true,\n              value: formData.confirmPassword,\n              onChange: handleChange,\n              className: `input-field ${validationErrors.confirmPassword ? 'border-red-500' : ''}`,\n              placeholder: \"Confirm your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), validationErrors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: validationErrors.confirmPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), formData.role === 'doctor' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"specialty\",\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Specialty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"specialty\",\n                name: \"specialty\",\n                required: true,\n                value: formData.specialty,\n                onChange: handleChange,\n                className: `input-field ${validationErrors.specialty ? 'border-red-500' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select your specialty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this), specialties.map(specialty => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: specialty,\n                  children: specialty\n                }, specialty, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), validationErrors.specialty && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: validationErrors.specialty\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"qualifications\",\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Qualifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"qualifications\",\n                name: \"qualifications\",\n                required: true,\n                value: formData.qualifications,\n                onChange: handleChange,\n                rows: 3,\n                className: `input-field ${validationErrors.qualifications ? 'border-red-500' : ''}`,\n                placeholder: \"Enter your qualifications (e.g., MBBS, MD, etc.)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), validationErrors.qualifications && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm mt-1\",\n                children: validationErrors.qualifications\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"experienceInYears\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Experience (Years)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"experienceInYears\",\n                  name: \"experienceInYears\",\n                  type: \"number\",\n                  min: \"0\",\n                  max: \"50\",\n                  required: true,\n                  value: formData.experienceInYears,\n                  onChange: handleChange,\n                  className: `input-field ${validationErrors.experienceInYears ? 'border-red-500' : ''}`,\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this), validationErrors.experienceInYears && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-red-500 text-sm mt-1\",\n                  children: validationErrors.experienceInYears\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"consultationFee\",\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Consultation Fee ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"consultationFee\",\n                  name: \"consultationFee\",\n                  type: \"number\",\n                  min: \"0\",\n                  step: \"0.01\",\n                  required: true,\n                  value: formData.consultationFee,\n                  onChange: handleChange,\n                  className: `input-field ${validationErrors.consultationFee ? 'border-red-500' : ''}`,\n                  placeholder: \"0.00\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), validationErrors.consultationFee && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-red-500 text-sm mt-1\",\n                  children: validationErrors.consultationFee\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-800 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Note:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), \" Doctor accounts require admin verification before you can start accepting appointments.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full border-t border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex justify-center text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 bg-white text-gray-500\",\n                children: \"Already have an account?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200\",\n              children: \"Sign in instead\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"B9+0CR3YsS+Yztw/LcPwrl2oCOY=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useAuth", "doctor<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RegisterPage", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "role", "specialty", "qualifications", "experienceInYears", "consultationFee", "specialties", "setSpecialties", "loading", "setLoading", "validationErrors", "setValidationErrors", "register", "isAuthenticated", "error", "clearError", "navigate", "fetchSpecialties", "response", "getSpecialties", "data", "console", "handleChange", "e", "value", "target", "validateForm", "errors", "trim", "length", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "parseInt", "parseFloat", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "checked", "onChange", "htmlFor", "id", "required", "placeholder", "map", "rows", "min", "max", "step", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/pages/RegisterPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { doctorAPI } from '../services/api';\n\nconst RegisterPage = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'patient',\n    // Doctor specific fields\n    specialty: '',\n    qualifications: '',\n    experienceInYears: '',\n    consultationFee: '',\n  });\n  const [specialties, setSpecialties] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [validationErrors, setValidationErrors] = useState({});\n  \n  const { register, isAuthenticated, error, clearError } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  // Fetch specialties for doctors\n  useEffect(() => {\n    const fetchSpecialties = async () => {\n      try {\n        const response = await doctorAPI.getSpecialties();\n        setSpecialties(response.data.data.specialties);\n      } catch (error) {\n        console.error('Error fetching specialties:', error);\n      }\n    };\n    fetchSpecialties();\n  }, []);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value,\n    });\n    \n    // Clear validation error for this field\n    if (validationErrors[name]) {\n      setValidationErrors({\n        ...validationErrors,\n        [name]: '',\n      });\n    }\n  };\n\n  const validateForm = () => {\n    const errors = {};\n\n    // Basic validation\n    if (!formData.name.trim()) errors.name = 'Name is required';\n    if (!formData.email.trim()) errors.email = 'Email is required';\n    if (!formData.password) errors.password = 'Password is required';\n    if (formData.password.length < 6) errors.password = 'Password must be at least 6 characters';\n    if (formData.password !== formData.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n\n    // Doctor specific validation\n    if (formData.role === 'doctor') {\n      if (!formData.specialty) errors.specialty = 'Specialty is required';\n      if (!formData.qualifications.trim()) errors.qualifications = 'Qualifications are required';\n      if (!formData.experienceInYears) errors.experienceInYears = 'Experience is required';\n      if (!formData.consultationFee) errors.consultationFee = 'Consultation fee is required';\n      \n      if (formData.experienceInYears && (formData.experienceInYears < 0 || formData.experienceInYears > 50)) {\n        errors.experienceInYears = 'Experience must be between 0 and 50 years';\n      }\n      \n      if (formData.consultationFee && formData.consultationFee < 0) {\n        errors.consultationFee = 'Consultation fee cannot be negative';\n      }\n    }\n\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    // Prepare data for submission\n    const submitData = {\n      name: formData.name.trim(),\n      email: formData.email.trim(),\n      password: formData.password,\n      role: formData.role,\n    };\n\n    // Add doctor specific fields\n    if (formData.role === 'doctor') {\n      submitData.specialty = formData.specialty;\n      submitData.qualifications = formData.qualifications.trim();\n      submitData.experienceInYears = parseInt(formData.experienceInYears);\n      submitData.consultationFee = parseFloat(formData.consultationFee);\n    }\n\n    const result = await register(submitData);\n    \n    if (result.success) {\n      navigate('/');\n    }\n    \n    setLoading(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"text-center\">\n          <div className=\"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-white font-bold text-xl\">D</span>\n          </div>\n          <h2 className=\"text-3xl font-bold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-gray-600\">\n            Join DocBook to manage your healthcare\n          </p>\n        </div>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {/* Error Message */}\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-800 text-sm\">{error}</p>\n              </div>\n            )}\n\n            {/* Role Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                I am a\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"role\"\n                    value=\"patient\"\n                    checked={formData.role === 'patient'}\n                    onChange={handleChange}\n                    className=\"mr-2\"\n                  />\n                  Patient\n                </label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    name=\"role\"\n                    value=\"doctor\"\n                    checked={formData.role === 'doctor'}\n                    onChange={handleChange}\n                    className=\"mr-2\"\n                  />\n                  Doctor\n                </label>\n              </div>\n            </div>\n\n            {/* Name Field */}\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                className={`input-field ${validationErrors.name ? 'border-red-500' : ''}`}\n                placeholder=\"Enter your full name\"\n              />\n              {validationErrors.name && (\n                <p className=\"text-red-500 text-sm mt-1\">{validationErrors.name}</p>\n              )}\n            </div>\n\n            {/* Email Field */}\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                className={`input-field ${validationErrors.email ? 'border-red-500' : ''}`}\n                placeholder=\"Enter your email\"\n              />\n              {validationErrors.email && (\n                <p className=\"text-red-500 text-sm mt-1\">{validationErrors.email}</p>\n              )}\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                value={formData.password}\n                onChange={handleChange}\n                className={`input-field ${validationErrors.password ? 'border-red-500' : ''}`}\n                placeholder=\"Enter your password\"\n              />\n              {validationErrors.password && (\n                <p className=\"text-red-500 text-sm mt-1\">{validationErrors.password}</p>\n              )}\n            </div>\n\n            {/* Confirm Password Field */}\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                required\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                className={`input-field ${validationErrors.confirmPassword ? 'border-red-500' : ''}`}\n                placeholder=\"Confirm your password\"\n              />\n              {validationErrors.confirmPassword && (\n                <p className=\"text-red-500 text-sm mt-1\">{validationErrors.confirmPassword}</p>\n              )}\n            </div>\n\n            {/* Doctor Specific Fields */}\n            {formData.role === 'doctor' && (\n              <>\n                <div>\n                  <label htmlFor=\"specialty\" className=\"block text-sm font-medium text-gray-700\">\n                    Specialty\n                  </label>\n                  <select\n                    id=\"specialty\"\n                    name=\"specialty\"\n                    required\n                    value={formData.specialty}\n                    onChange={handleChange}\n                    className={`input-field ${validationErrors.specialty ? 'border-red-500' : ''}`}\n                  >\n                    <option value=\"\">Select your specialty</option>\n                    {specialties.map(specialty => (\n                      <option key={specialty} value={specialty}>\n                        {specialty}\n                      </option>\n                    ))}\n                  </select>\n                  {validationErrors.specialty && (\n                    <p className=\"text-red-500 text-sm mt-1\">{validationErrors.specialty}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label htmlFor=\"qualifications\" className=\"block text-sm font-medium text-gray-700\">\n                    Qualifications\n                  </label>\n                  <textarea\n                    id=\"qualifications\"\n                    name=\"qualifications\"\n                    required\n                    value={formData.qualifications}\n                    onChange={handleChange}\n                    rows={3}\n                    className={`input-field ${validationErrors.qualifications ? 'border-red-500' : ''}`}\n                    placeholder=\"Enter your qualifications (e.g., MBBS, MD, etc.)\"\n                  />\n                  {validationErrors.qualifications && (\n                    <p className=\"text-red-500 text-sm mt-1\">{validationErrors.qualifications}</p>\n                  )}\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"experienceInYears\" className=\"block text-sm font-medium text-gray-700\">\n                      Experience (Years)\n                    </label>\n                    <input\n                      id=\"experienceInYears\"\n                      name=\"experienceInYears\"\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"50\"\n                      required\n                      value={formData.experienceInYears}\n                      onChange={handleChange}\n                      className={`input-field ${validationErrors.experienceInYears ? 'border-red-500' : ''}`}\n                      placeholder=\"0\"\n                    />\n                    {validationErrors.experienceInYears && (\n                      <p className=\"text-red-500 text-sm mt-1\">{validationErrors.experienceInYears}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"consultationFee\" className=\"block text-sm font-medium text-gray-700\">\n                      Consultation Fee ($)\n                    </label>\n                    <input\n                      id=\"consultationFee\"\n                      name=\"consultationFee\"\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      required\n                      value={formData.consultationFee}\n                      onChange={handleChange}\n                      className={`input-field ${validationErrors.consultationFee ? 'border-red-500' : ''}`}\n                      placeholder=\"0.00\"\n                    />\n                    {validationErrors.consultationFee && (\n                      <p className=\"text-red-500 text-sm mt-1\">{validationErrors.consultationFee}</p>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                  <p className=\"text-yellow-800 text-sm\">\n                    <strong>Note:</strong> Doctor accounts require admin verification before you can start accepting appointments.\n                  </p>\n                </div>\n              </>\n            )}\n\n            {/* Submit Button */}\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creating account...\n                  </div>\n                ) : (\n                  'Create account'\n                )}\n              </button>\n            </div>\n          </form>\n\n          {/* Sign in link */}\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Already have an account?</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <Link\n                to=\"/login\"\n                className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200\"\n              >\n                Sign in instead\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACvCc,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,SAAS;IACf;IACAC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE5D,MAAM;IAAE6B,QAAQ;IAAEC,eAAe;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAClE,MAAM6B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,IAAI6B,eAAe,EAAE;MACnBG,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC,EAAE,CAACH,eAAe,EAAEG,QAAQ,CAAC,CAAC;;EAE/B;EACAhC,SAAS,CAAC,MAAM;IACd+B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMiC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM9B,SAAS,CAAC+B,cAAc,CAAC,CAAC;QACjDZ,cAAc,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,WAAW,CAAC;MAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdO,OAAO,CAACP,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF,CAAC;IACDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC7B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAG2B;IACV,CAAC,CAAC;;IAEF;IACA,IAAId,gBAAgB,CAACb,IAAI,CAAC,EAAE;MAC1Bc,mBAAmB,CAAC;QAClB,GAAGD,gBAAgB;QACnB,CAACb,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAChC,QAAQ,CAACE,IAAI,CAAC+B,IAAI,CAAC,CAAC,EAAED,MAAM,CAAC9B,IAAI,GAAG,kBAAkB;IAC3D,IAAI,CAACF,QAAQ,CAACG,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAED,MAAM,CAAC7B,KAAK,GAAG,mBAAmB;IAC9D,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,GAAG,sBAAsB;IAChE,IAAIJ,QAAQ,CAACI,QAAQ,CAAC8B,MAAM,GAAG,CAAC,EAAEF,MAAM,CAAC5B,QAAQ,GAAG,wCAAwC;IAC5F,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClD2B,MAAM,CAAC3B,eAAe,GAAG,wBAAwB;IACnD;;IAEA;IACA,IAAIL,QAAQ,CAACM,IAAI,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACN,QAAQ,CAACO,SAAS,EAAEyB,MAAM,CAACzB,SAAS,GAAG,uBAAuB;MACnE,IAAI,CAACP,QAAQ,CAACQ,cAAc,CAACyB,IAAI,CAAC,CAAC,EAAED,MAAM,CAACxB,cAAc,GAAG,6BAA6B;MAC1F,IAAI,CAACR,QAAQ,CAACS,iBAAiB,EAAEuB,MAAM,CAACvB,iBAAiB,GAAG,wBAAwB;MACpF,IAAI,CAACT,QAAQ,CAACU,eAAe,EAAEsB,MAAM,CAACtB,eAAe,GAAG,8BAA8B;MAEtF,IAAIV,QAAQ,CAACS,iBAAiB,KAAKT,QAAQ,CAACS,iBAAiB,GAAG,CAAC,IAAIT,QAAQ,CAACS,iBAAiB,GAAG,EAAE,CAAC,EAAE;QACrGuB,MAAM,CAACvB,iBAAiB,GAAG,2CAA2C;MACxE;MAEA,IAAIT,QAAQ,CAACU,eAAe,IAAIV,QAAQ,CAACU,eAAe,GAAG,CAAC,EAAE;QAC5DsB,MAAM,CAACtB,eAAe,GAAG,qCAAqC;MAChE;IACF;IAEAM,mBAAmB,CAACgB,MAAM,CAAC;IAC3B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACE,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAjB,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMyB,UAAU,GAAG;MACjBrC,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC+B,IAAI,CAAC,CAAC;MAC1B9B,KAAK,EAAEH,QAAQ,CAACG,KAAK,CAAC8B,IAAI,CAAC,CAAC;MAC5B7B,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;MAC3BE,IAAI,EAAEN,QAAQ,CAACM;IACjB,CAAC;;IAED;IACA,IAAIN,QAAQ,CAACM,IAAI,KAAK,QAAQ,EAAE;MAC9BiC,UAAU,CAAChC,SAAS,GAAGP,QAAQ,CAACO,SAAS;MACzCgC,UAAU,CAAC/B,cAAc,GAAGR,QAAQ,CAACQ,cAAc,CAACyB,IAAI,CAAC,CAAC;MAC1DM,UAAU,CAAC9B,iBAAiB,GAAG+B,QAAQ,CAACxC,QAAQ,CAACS,iBAAiB,CAAC;MACnE8B,UAAU,CAAC7B,eAAe,GAAG+B,UAAU,CAACzC,QAAQ,CAACU,eAAe,CAAC;IACnE;IAEA,MAAMgC,MAAM,GAAG,MAAMzB,QAAQ,CAACsB,UAAU,CAAC;IAEzC,IAAIG,MAAM,CAACC,OAAO,EAAE;MAClBtB,QAAQ,CAAC,GAAG,CAAC;IACf;IAEAP,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEnB,OAAA;IAAKiD,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzFlD,OAAA;MAAKiD,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/ClD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAKiD,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAChGlD,OAAA;YAAMiD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNtD,OAAA;UAAIiD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAGiD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA;MAAKiD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlD,OAAA;QAAKiD,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DlD,OAAA;UAAMiD,SAAS,EAAC,WAAW;UAACM,QAAQ,EAAEb,YAAa;UAAAQ,QAAA,GAEhD1B,KAAK,iBACJxB,OAAA;YAAKiD,SAAS,EAAC,gDAAgD;YAAAC,QAAA,eAC7DlD,OAAA;cAAGiD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAE1B;YAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,eAGDtD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAOiD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cAAKiD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlD,OAAA;gBAAOiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClClD,OAAA;kBACEwD,IAAI,EAAC,OAAO;kBACZjD,IAAI,EAAC,MAAM;kBACX2B,KAAK,EAAC,SAAS;kBACfuB,OAAO,EAAEpD,QAAQ,CAACM,IAAI,KAAK,SAAU;kBACrC+C,QAAQ,EAAE1B,YAAa;kBACvBiB,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,WAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBAAOiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClClD,OAAA;kBACEwD,IAAI,EAAC,OAAO;kBACZjD,IAAI,EAAC,MAAM;kBACX2B,KAAK,EAAC,QAAQ;kBACduB,OAAO,EAAEpD,QAAQ,CAACM,IAAI,KAAK,QAAS;kBACpC+C,QAAQ,EAAE1B,YAAa;kBACvBiB,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,UAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAO2D,OAAO,EAAC,MAAM;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE1E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE4D,EAAE,EAAC,MAAM;cACTrD,IAAI,EAAC,MAAM;cACXiD,IAAI,EAAC,MAAM;cACXK,QAAQ;cACR3B,KAAK,EAAE7B,QAAQ,CAACE,IAAK;cACrBmD,QAAQ,EAAE1B,YAAa;cACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACb,IAAI,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC1EuD,WAAW,EAAC;YAAsB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACDlC,gBAAgB,CAACb,IAAI,iBACpBP,OAAA;cAAGiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9B,gBAAgB,CAACb;YAAI;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACpE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAO2D,OAAO,EAAC,OAAO;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE4D,EAAE,EAAC,OAAO;cACVrD,IAAI,EAAC,OAAO;cACZiD,IAAI,EAAC,OAAO;cACZK,QAAQ;cACR3B,KAAK,EAAE7B,QAAQ,CAACG,KAAM;cACtBkD,QAAQ,EAAE1B,YAAa;cACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACZ,KAAK,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC3EsD,WAAW,EAAC;YAAkB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACDlC,gBAAgB,CAACZ,KAAK,iBACrBR,OAAA;cAAGiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9B,gBAAgB,CAACZ;YAAK;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAO2D,OAAO,EAAC,UAAU;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE4D,EAAE,EAAC,UAAU;cACbrD,IAAI,EAAC,UAAU;cACfiD,IAAI,EAAC,UAAU;cACfK,QAAQ;cACR3B,KAAK,EAAE7B,QAAQ,CAACI,QAAS;cACzBiD,QAAQ,EAAE1B,YAAa;cACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACX,QAAQ,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC9EqD,WAAW,EAAC;YAAqB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACDlC,gBAAgB,CAACX,QAAQ,iBACxBT,OAAA;cAAGiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9B,gBAAgB,CAACX;YAAQ;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACxE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAO2D,OAAO,EAAC,iBAAiB;cAACV,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtD,OAAA;cACE4D,EAAE,EAAC,iBAAiB;cACpBrD,IAAI,EAAC,iBAAiB;cACtBiD,IAAI,EAAC,UAAU;cACfK,QAAQ;cACR3B,KAAK,EAAE7B,QAAQ,CAACK,eAAgB;cAChCgD,QAAQ,EAAE1B,YAAa;cACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACV,eAAe,GAAG,gBAAgB,GAAG,EAAE,EAAG;cACrFoD,WAAW,EAAC;YAAuB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACDlC,gBAAgB,CAACV,eAAe,iBAC/BV,OAAA;cAAGiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE9B,gBAAgB,CAACV;YAAe;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC/E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLjD,QAAQ,CAACM,IAAI,KAAK,QAAQ,iBACzBX,OAAA,CAAAE,SAAA;YAAAgD,QAAA,gBACElD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAO2D,OAAO,EAAC,WAAW;gBAACV,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAE/E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBACE4D,EAAE,EAAC,WAAW;gBACdrD,IAAI,EAAC,WAAW;gBAChBsD,QAAQ;gBACR3B,KAAK,EAAE7B,QAAQ,CAACO,SAAU;gBAC1B8C,QAAQ,EAAE1B,YAAa;gBACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACR,SAAS,GAAG,gBAAgB,GAAG,EAAE,EAAG;gBAAAsC,QAAA,gBAE/ElD,OAAA;kBAAQkC,KAAK,EAAC,EAAE;kBAAAgB,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC9CtC,WAAW,CAAC+C,GAAG,CAACnD,SAAS,iBACxBZ,OAAA;kBAAwBkC,KAAK,EAAEtB,SAAU;kBAAAsC,QAAA,EACtCtC;gBAAS,GADCA,SAAS;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRlC,gBAAgB,CAACR,SAAS,iBACzBZ,OAAA;gBAAGiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9B,gBAAgB,CAACR;cAAS;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAO2D,OAAO,EAAC,gBAAgB;gBAACV,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBACE4D,EAAE,EAAC,gBAAgB;gBACnBrD,IAAI,EAAC,gBAAgB;gBACrBsD,QAAQ;gBACR3B,KAAK,EAAE7B,QAAQ,CAACQ,cAAe;gBAC/B6C,QAAQ,EAAE1B,YAAa;gBACvBgC,IAAI,EAAE,CAAE;gBACRf,SAAS,EAAE,eAAe7B,gBAAgB,CAACP,cAAc,GAAG,gBAAgB,GAAG,EAAE,EAAG;gBACpFiD,WAAW,EAAC;cAAkD;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACDlC,gBAAgB,CAACP,cAAc,iBAC9Bb,OAAA;gBAAGiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE9B,gBAAgB,CAACP;cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO2D,OAAO,EAAC,mBAAmB;kBAACV,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEvF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,EAAE,EAAC,mBAAmB;kBACtBrD,IAAI,EAAC,mBAAmB;kBACxBiD,IAAI,EAAC,QAAQ;kBACbS,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAC,IAAI;kBACRL,QAAQ;kBACR3B,KAAK,EAAE7B,QAAQ,CAACS,iBAAkB;kBAClC4C,QAAQ,EAAE1B,YAAa;kBACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACN,iBAAiB,GAAG,gBAAgB,GAAG,EAAE,EAAG;kBACvFgD,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,EACDlC,gBAAgB,CAACN,iBAAiB,iBACjCd,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE9B,gBAAgB,CAACN;gBAAiB;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACjF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAO2D,OAAO,EAAC,iBAAiB;kBAACV,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAErF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,EAAE,EAAC,iBAAiB;kBACpBrD,IAAI,EAAC,iBAAiB;kBACtBiD,IAAI,EAAC,QAAQ;kBACbS,GAAG,EAAC,GAAG;kBACPE,IAAI,EAAC,MAAM;kBACXN,QAAQ;kBACR3B,KAAK,EAAE7B,QAAQ,CAACU,eAAgB;kBAChC2C,QAAQ,EAAE1B,YAAa;kBACvBiB,SAAS,EAAE,eAAe7B,gBAAgB,CAACL,eAAe,GAAG,gBAAgB,GAAG,EAAE,EAAG;kBACrF+C,WAAW,EAAC;gBAAM;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,EACDlC,gBAAgB,CAACL,eAAe,iBAC/Bf,OAAA;kBAAGiD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE9B,gBAAgB,CAACL;gBAAe;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnElD,OAAA;gBAAGiD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACpClD,OAAA;kBAAAkD,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4FACxB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH,eAGDtD,OAAA;YAAAkD,QAAA,eACElD,OAAA;cACEwD,IAAI,EAAC,QAAQ;cACbY,QAAQ,EAAElD,OAAQ;cAClB+B,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAE7EhC,OAAO,gBACNlB,OAAA;gBAAKiD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/ClD,OAAA;kBAAKiD,SAAS,EAAC;gBAAgE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uBAExF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPtD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlD,OAAA;YAAKiD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlD,OAAA;cAAKiD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDlD,OAAA;gBAAKiD,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNtD,OAAA;cAAKiD,SAAS,EAAC,sCAAsC;cAAAC,QAAA,eACnDlD,OAAA;gBAAMiD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAKiD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlD,OAAA,CAACL,IAAI;cACH0E,EAAE,EAAC,QAAQ;cACXpB,SAAS,EAAC,mKAAmK;cAAAC,QAAA,EAC9K;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CArZID,YAAY;EAAA,QAiByCN,OAAO,EAC/CD,WAAW;AAAA;AAAA0E,EAAA,GAlBxBnE,YAAY;AAuZlB,eAAeA,YAAY;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}