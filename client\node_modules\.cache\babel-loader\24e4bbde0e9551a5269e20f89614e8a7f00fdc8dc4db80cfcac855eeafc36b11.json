{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar aCallable = require('../internals/a-callable');\nvar NATIVE_BIND = require('../internals/function-bind-native');\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : NATIVE_BIND ? bind(fn, that) : function /* ...args */\n  () {\n    return fn.apply(that, arguments);\n  };\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "aCallable", "NATIVE_BIND", "bind", "module", "exports", "fn", "that", "undefined", "apply", "arguments"], "sources": ["E:/03/client/node_modules/core-js-pure/internals/function-bind-context.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar aCallable = require('../internals/a-callable');\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : NATIVE_BIND ? bind(fn, that) : function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AACtE,IAAIC,SAAS,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAClD,IAAIE,WAAW,GAAGF,OAAO,CAAC,mCAAmC,CAAC;AAE9D,IAAIG,IAAI,GAAGJ,WAAW,CAACA,WAAW,CAACI,IAAI,CAAC;;AAExC;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAEC,IAAI,EAAE;EACnCN,SAAS,CAACK,EAAE,CAAC;EACb,OAAOC,IAAI,KAAKC,SAAS,GAAGF,EAAE,GAAGJ,WAAW,GAAGC,IAAI,CAACG,EAAE,EAAEC,IAAI,CAAC,GAAG,SAAU;EAAA,GAAe;IACvF,OAAOD,EAAE,CAACG,KAAK,CAACF,IAAI,EAAEG,SAAS,CAAC;EAClC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}