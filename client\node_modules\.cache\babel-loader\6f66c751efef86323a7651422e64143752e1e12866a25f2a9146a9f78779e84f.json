{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\context\\\\SocketContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SocketContext = /*#__PURE__*/createContext();\nexport const useSocket = () => {\n  _s();\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n_s(useSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SocketProvider = ({\n  children\n}) => {\n  _s2();\n  const [socket, setSocket] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [incomingCall, setIncomingCall] = useState(null);\n  const [activeCall, setActiveCall] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  const {\n    user,\n    token,\n    isAuthenticated\n  } = useAuth();\n  useEffect(() => {\n    if (isAuthenticated && token && user) {\n      var _process$env$REACT_AP;\n      // Initialize socket connection\n      const newSocket = io(((_process$env$REACT_AP = process.env.REACT_APP_API_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace('/api', '')) || 'http://localhost:5000', {\n        auth: {\n          token: token\n        },\n        transports: ['websocket', 'polling'],\n        forceNew: true\n      });\n      newSocket.on('connect', () => {\n        console.log('Connected to server');\n        setIsConnected(true);\n      });\n      newSocket.on('disconnect', () => {\n        console.log('Disconnected from server');\n        setIsConnected(false);\n      });\n      newSocket.on('connect_error', error => {\n        console.error('Socket connection error:', error);\n        setIsConnected(false);\n      });\n\n      // Handle user status changes\n      newSocket.on('user_status_changed', data => {\n        setOnlineUsers(prev => {\n          const filtered = prev.filter(u => u.userId !== data.userId);\n          if (data.status === 'online') {\n            return [...filtered, data];\n          }\n          return filtered;\n        });\n      });\n\n      // Handle incoming messages\n      newSocket.on('new_message', message => {\n        setMessages(prev => [...prev, message]);\n\n        // Add notification if message is not from current user\n        if (message.sender._id !== user._id) {\n          addNotification({\n            type: 'message',\n            title: 'New Message',\n            message: `${message.sender.name}: ${message.message.substring(0, 50)}...`,\n            timestamp: new Date(),\n            data: message\n          });\n        }\n      });\n\n      // Handle message notifications\n      newSocket.on('message_notification', notification => {\n        if (notification.senderId !== user._id) {\n          addNotification({\n            type: 'message',\n            title: 'New Message',\n            message: `${notification.senderName}: ${notification.message}`,\n            timestamp: notification.timestamp,\n            data: notification\n          });\n        }\n      });\n\n      // Handle incoming calls\n      newSocket.on('incoming_call', callData => {\n        setIncomingCall(callData);\n        addNotification({\n          type: 'call',\n          title: 'Incoming Call',\n          message: `${callData.caller.name} is calling you`,\n          timestamp: new Date(),\n          data: callData\n        });\n      });\n\n      // Handle call events\n      newSocket.on('call_answered', data => {\n        setActiveCall(prev => prev ? {\n          ...prev,\n          status: 'answered'\n        } : null);\n      });\n      newSocket.on('call_rejected', data => {\n        setIncomingCall(null);\n        setActiveCall(null);\n        addNotification({\n          type: 'call',\n          title: 'Call Rejected',\n          message: 'Your call was rejected',\n          timestamp: new Date()\n        });\n      });\n      newSocket.on('call_ended', data => {\n        setIncomingCall(null);\n        setActiveCall(null);\n        addNotification({\n          type: 'call',\n          title: 'Call Ended',\n          message: 'Call has ended',\n          timestamp: new Date()\n        });\n      });\n      newSocket.on('call_failed', data => {\n        setActiveCall(null);\n        addNotification({\n          type: 'error',\n          title: 'Call Failed',\n          message: data.reason || 'Call could not be completed',\n          timestamp: new Date()\n        });\n      });\n\n      // Handle typing indicators\n      newSocket.on('user_typing', data => {\n        // Handle typing indicator\n        console.log(`${data.userName} is typing...`);\n      });\n      newSocket.on('user_stopped_typing', data => {\n        // Handle stop typing\n        console.log(`User stopped typing`);\n      });\n\n      // Handle helpline messages\n      newSocket.on('new_helpline_message', message => {\n        addNotification({\n          type: 'helpline',\n          title: 'Helpline Response',\n          message: 'You have a new response from support',\n          timestamp: new Date(),\n          data: message\n        });\n      });\n      setSocket(newSocket);\n      return () => {\n        newSocket.close();\n        setSocket(null);\n        setIsConnected(false);\n      };\n    }\n  }, [isAuthenticated, token, user]);\n  const addNotification = notification => {\n    const id = Date.now().toString();\n    const newNotification = {\n      ...notification,\n      id\n    };\n    setNotifications(prev => [newNotification, ...prev.slice(0, 9)]); // Keep only 10 notifications\n\n    // Auto-remove notification after 5 seconds\n    setTimeout(() => {\n      removeNotification(id);\n    }, 5000);\n  };\n  const removeNotification = id => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n  const clearAllNotifications = () => {\n    setNotifications([]);\n  };\n\n  // Socket utility functions\n  const joinChat = receiverId => {\n    if (socket) {\n      socket.emit('join_chat', {\n        receiverId\n      });\n    }\n  };\n  const sendMessage = (receiverId, message, messageType = 'text', appointmentId = null) => {\n    if (socket) {\n      socket.emit('send_message', {\n        receiverId,\n        message,\n        messageType,\n        appointmentId\n      });\n    }\n  };\n  const markMessagesAsRead = senderId => {\n    if (socket) {\n      socket.emit('mark_messages_read', {\n        senderId\n      });\n    }\n  };\n  const initiateCall = (receiverId, callType, appointmentId = null) => {\n    if (socket) {\n      socket.emit('initiate_call', {\n        receiverId,\n        callType,\n        appointmentId\n      });\n    }\n  };\n  const answerCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('answer_call', {\n        callId,\n        roomId\n      });\n      setActiveCall({\n        callId,\n        roomId,\n        status: 'answered'\n      });\n      setIncomingCall(null);\n    }\n  };\n  const rejectCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('reject_call', {\n        callId,\n        roomId\n      });\n      setIncomingCall(null);\n    }\n  };\n  const endCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('end_call', {\n        callId,\n        roomId\n      });\n      setActiveCall(null);\n    }\n  };\n  const startTyping = receiverId => {\n    if (socket) {\n      socket.emit('typing_start', {\n        receiverId\n      });\n    }\n  };\n  const stopTyping = receiverId => {\n    if (socket) {\n      socket.emit('typing_stop', {\n        receiverId\n      });\n    }\n  };\n  const joinHelpline = () => {\n    if (socket) {\n      socket.emit('join_helpline');\n    }\n  };\n  const sendHelplineMessage = (message, ticketId) => {\n    if (socket) {\n      socket.emit('helpline_message', {\n        message,\n        ticketId\n      });\n    }\n  };\n\n  // WebRTC signaling functions\n  const sendWebRTCOffer = (roomId, offer) => {\n    if (socket) {\n      socket.emit('webrtc_offer', {\n        roomId,\n        offer\n      });\n    }\n  };\n  const sendWebRTCAnswer = (roomId, answer) => {\n    if (socket) {\n      socket.emit('webrtc_answer', {\n        roomId,\n        answer\n      });\n    }\n  };\n  const sendICECandidate = (roomId, candidate) => {\n    if (socket) {\n      socket.emit('webrtc_ice_candidate', {\n        roomId,\n        candidate\n      });\n    }\n  };\n  const value = {\n    socket,\n    isConnected,\n    onlineUsers,\n    incomingCall,\n    activeCall,\n    messages,\n    notifications,\n    // Utility functions\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    // Chat functions\n    joinChat,\n    sendMessage,\n    markMessagesAsRead,\n    startTyping,\n    stopTyping,\n    // Call functions\n    initiateCall,\n    answerCall,\n    rejectCall,\n    endCall,\n    // Helpline functions\n    joinHelpline,\n    sendHelplineMessage,\n    // WebRTC functions\n    sendWebRTCOffer,\n    sendWebRTCAnswer,\n    sendICECandidate,\n    // State setters\n    setActiveCall,\n    setIncomingCall,\n    setMessages\n  };\n  return /*#__PURE__*/_jsxDEV(SocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s2(SocketProvider, \"tnoYW8B5RuDg87SmAJzJDhRqY4E=\", false, function () {\n  return [useAuth];\n});\n_c = SocketProvider;\nvar _c;\n$RefreshReg$(_c, \"SocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "useAuth", "jsxDEV", "_jsxDEV", "SocketContext", "useSocket", "_s", "context", "Error", "SocketProvider", "children", "_s2", "socket", "setSocket", "isConnected", "setIsConnected", "onlineUsers", "setOnlineUsers", "incomingCall", "setIncomingCall", "activeCall", "setActiveCall", "messages", "setMessages", "notifications", "setNotifications", "user", "token", "isAuthenticated", "_process$env$REACT_AP", "newSocket", "process", "env", "REACT_APP_API_URL", "replace", "auth", "transports", "forceNew", "on", "console", "log", "error", "data", "prev", "filtered", "filter", "u", "userId", "status", "message", "sender", "_id", "addNotification", "type", "title", "name", "substring", "timestamp", "Date", "notification", "senderId", "sender<PERSON>ame", "callData", "caller", "reason", "userName", "close", "id", "now", "toString", "newNotification", "slice", "setTimeout", "removeNotification", "n", "clearAllNotifications", "joinChat", "receiverId", "emit", "sendMessage", "messageType", "appointmentId", "markMessagesAsRead", "initiateCall", "callType", "answerCall", "callId", "roomId", "rejectCall", "endCall", "startTyping", "stopTyping", "joinHelpline", "sendHelplineMessage", "ticketId", "sendWebRTCOffer", "offer", "sendWebRTCAnswer", "answer", "sendICECandidate", "candidate", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/context/SocketContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { io } from 'socket.io-client';\nimport { useAuth } from './AuthContext';\n\nconst SocketContext = createContext();\n\nexport const useSocket = () => {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n\nexport const SocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [incomingCall, setIncomingCall] = useState(null);\n  const [activeCall, setActiveCall] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [notifications, setNotifications] = useState([]);\n  \n  const { user, token, isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (isAuthenticated && token && user) {\n      // Initialize socket connection\n      const newSocket = io(process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:5000', {\n        auth: {\n          token: token\n        },\n        transports: ['websocket', 'polling'],\n        forceNew: true\n      });\n\n      newSocket.on('connect', () => {\n        console.log('Connected to server');\n        setIsConnected(true);\n      });\n\n      newSocket.on('disconnect', () => {\n        console.log('Disconnected from server');\n        setIsConnected(false);\n      });\n\n      newSocket.on('connect_error', (error) => {\n        console.error('Socket connection error:', error);\n        setIsConnected(false);\n      });\n\n      // Handle user status changes\n      newSocket.on('user_status_changed', (data) => {\n        setOnlineUsers(prev => {\n          const filtered = prev.filter(u => u.userId !== data.userId);\n          if (data.status === 'online') {\n            return [...filtered, data];\n          }\n          return filtered;\n        });\n      });\n\n      // Handle incoming messages\n      newSocket.on('new_message', (message) => {\n        setMessages(prev => [...prev, message]);\n        \n        // Add notification if message is not from current user\n        if (message.sender._id !== user._id) {\n          addNotification({\n            type: 'message',\n            title: 'New Message',\n            message: `${message.sender.name}: ${message.message.substring(0, 50)}...`,\n            timestamp: new Date(),\n            data: message\n          });\n        }\n      });\n\n      // Handle message notifications\n      newSocket.on('message_notification', (notification) => {\n        if (notification.senderId !== user._id) {\n          addNotification({\n            type: 'message',\n            title: 'New Message',\n            message: `${notification.senderName}: ${notification.message}`,\n            timestamp: notification.timestamp,\n            data: notification\n          });\n        }\n      });\n\n      // Handle incoming calls\n      newSocket.on('incoming_call', (callData) => {\n        setIncomingCall(callData);\n        addNotification({\n          type: 'call',\n          title: 'Incoming Call',\n          message: `${callData.caller.name} is calling you`,\n          timestamp: new Date(),\n          data: callData\n        });\n      });\n\n      // Handle call events\n      newSocket.on('call_answered', (data) => {\n        setActiveCall(prev => prev ? { ...prev, status: 'answered' } : null);\n      });\n\n      newSocket.on('call_rejected', (data) => {\n        setIncomingCall(null);\n        setActiveCall(null);\n        addNotification({\n          type: 'call',\n          title: 'Call Rejected',\n          message: 'Your call was rejected',\n          timestamp: new Date()\n        });\n      });\n\n      newSocket.on('call_ended', (data) => {\n        setIncomingCall(null);\n        setActiveCall(null);\n        addNotification({\n          type: 'call',\n          title: 'Call Ended',\n          message: 'Call has ended',\n          timestamp: new Date()\n        });\n      });\n\n      newSocket.on('call_failed', (data) => {\n        setActiveCall(null);\n        addNotification({\n          type: 'error',\n          title: 'Call Failed',\n          message: data.reason || 'Call could not be completed',\n          timestamp: new Date()\n        });\n      });\n\n      // Handle typing indicators\n      newSocket.on('user_typing', (data) => {\n        // Handle typing indicator\n        console.log(`${data.userName} is typing...`);\n      });\n\n      newSocket.on('user_stopped_typing', (data) => {\n        // Handle stop typing\n        console.log(`User stopped typing`);\n      });\n\n      // Handle helpline messages\n      newSocket.on('new_helpline_message', (message) => {\n        addNotification({\n          type: 'helpline',\n          title: 'Helpline Response',\n          message: 'You have a new response from support',\n          timestamp: new Date(),\n          data: message\n        });\n      });\n\n      setSocket(newSocket);\n\n      return () => {\n        newSocket.close();\n        setSocket(null);\n        setIsConnected(false);\n      };\n    }\n  }, [isAuthenticated, token, user]);\n\n  const addNotification = (notification) => {\n    const id = Date.now().toString();\n    const newNotification = { ...notification, id };\n    \n    setNotifications(prev => [newNotification, ...prev.slice(0, 9)]); // Keep only 10 notifications\n    \n    // Auto-remove notification after 5 seconds\n    setTimeout(() => {\n      removeNotification(id);\n    }, 5000);\n  };\n\n  const removeNotification = (id) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const clearAllNotifications = () => {\n    setNotifications([]);\n  };\n\n  // Socket utility functions\n  const joinChat = (receiverId) => {\n    if (socket) {\n      socket.emit('join_chat', { receiverId });\n    }\n  };\n\n  const sendMessage = (receiverId, message, messageType = 'text', appointmentId = null) => {\n    if (socket) {\n      socket.emit('send_message', {\n        receiverId,\n        message,\n        messageType,\n        appointmentId\n      });\n    }\n  };\n\n  const markMessagesAsRead = (senderId) => {\n    if (socket) {\n      socket.emit('mark_messages_read', { senderId });\n    }\n  };\n\n  const initiateCall = (receiverId, callType, appointmentId = null) => {\n    if (socket) {\n      socket.emit('initiate_call', {\n        receiverId,\n        callType,\n        appointmentId\n      });\n    }\n  };\n\n  const answerCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('answer_call', { callId, roomId });\n      setActiveCall({ callId, roomId, status: 'answered' });\n      setIncomingCall(null);\n    }\n  };\n\n  const rejectCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('reject_call', { callId, roomId });\n      setIncomingCall(null);\n    }\n  };\n\n  const endCall = (callId, roomId) => {\n    if (socket) {\n      socket.emit('end_call', { callId, roomId });\n      setActiveCall(null);\n    }\n  };\n\n  const startTyping = (receiverId) => {\n    if (socket) {\n      socket.emit('typing_start', { receiverId });\n    }\n  };\n\n  const stopTyping = (receiverId) => {\n    if (socket) {\n      socket.emit('typing_stop', { receiverId });\n    }\n  };\n\n  const joinHelpline = () => {\n    if (socket) {\n      socket.emit('join_helpline');\n    }\n  };\n\n  const sendHelplineMessage = (message, ticketId) => {\n    if (socket) {\n      socket.emit('helpline_message', { message, ticketId });\n    }\n  };\n\n  // WebRTC signaling functions\n  const sendWebRTCOffer = (roomId, offer) => {\n    if (socket) {\n      socket.emit('webrtc_offer', { roomId, offer });\n    }\n  };\n\n  const sendWebRTCAnswer = (roomId, answer) => {\n    if (socket) {\n      socket.emit('webrtc_answer', { roomId, answer });\n    }\n  };\n\n  const sendICECandidate = (roomId, candidate) => {\n    if (socket) {\n      socket.emit('webrtc_ice_candidate', { roomId, candidate });\n    }\n  };\n\n  const value = {\n    socket,\n    isConnected,\n    onlineUsers,\n    incomingCall,\n    activeCall,\n    messages,\n    notifications,\n    \n    // Utility functions\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    \n    // Chat functions\n    joinChat,\n    sendMessage,\n    markMessagesAsRead,\n    startTyping,\n    stopTyping,\n    \n    // Call functions\n    initiateCall,\n    answerCall,\n    rejectCall,\n    endCall,\n    \n    // Helpline functions\n    joinHelpline,\n    sendHelplineMessage,\n    \n    // WebRTC functions\n    sendWebRTCOffer,\n    sendWebRTCAnswer,\n    sendICECandidate,\n    \n    // State setters\n    setActiveCall,\n    setIncomingCall,\n    setMessages\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,gBAAGR,aAAa,CAAC,CAAC;AAErC,OAAO,MAAMS,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,OAAO,GAAGV,UAAU,CAACO,aAAa,CAAC;EACzC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,SAAS;AAQtB,OAAO,MAAMI,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM;IAAE2B,IAAI;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAG3B,OAAO,CAAC,CAAC;EAElDH,SAAS,CAAC,MAAM;IACd,IAAI8B,eAAe,IAAID,KAAK,IAAID,IAAI,EAAE;MAAA,IAAAG,qBAAA;MACpC;MACA,MAAMC,SAAS,GAAG9B,EAAE,CAAC,EAAA6B,qBAAA,GAAAE,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,KAAI,uBAAuB,EAAE;QAClGC,IAAI,EAAE;UACJR,KAAK,EAAEA;QACT,CAAC;QACDS,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFP,SAAS,CAACQ,EAAE,CAAC,SAAS,EAAE,MAAM;QAC5BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClCzB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC;MAEFe,SAAS,CAACQ,EAAE,CAAC,YAAY,EAAE,MAAM;QAC/BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCzB,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,CAAC;MAEFe,SAAS,CAACQ,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;QACvCF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD1B,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,CAAC;;MAEF;MACAe,SAAS,CAACQ,EAAE,CAAC,qBAAqB,EAAGI,IAAI,IAAK;QAC5CzB,cAAc,CAAC0B,IAAI,IAAI;UACrB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKL,IAAI,CAACK,MAAM,CAAC;UAC3D,IAAIL,IAAI,CAACM,MAAM,KAAK,QAAQ,EAAE;YAC5B,OAAO,CAAC,GAAGJ,QAAQ,EAAEF,IAAI,CAAC;UAC5B;UACA,OAAOE,QAAQ;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAd,SAAS,CAACQ,EAAE,CAAC,aAAa,EAAGW,OAAO,IAAK;QACvC1B,WAAW,CAACoB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,OAAO,CAAC,CAAC;;QAEvC;QACA,IAAIA,OAAO,CAACC,MAAM,CAACC,GAAG,KAAKzB,IAAI,CAACyB,GAAG,EAAE;UACnCC,eAAe,CAAC;YACdC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,aAAa;YACpBL,OAAO,EAAE,GAAGA,OAAO,CAACC,MAAM,CAACK,IAAI,KAAKN,OAAO,CAACA,OAAO,CAACO,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;YACzEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;YACrBhB,IAAI,EAAEO;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACAnB,SAAS,CAACQ,EAAE,CAAC,sBAAsB,EAAGqB,YAAY,IAAK;QACrD,IAAIA,YAAY,CAACC,QAAQ,KAAKlC,IAAI,CAACyB,GAAG,EAAE;UACtCC,eAAe,CAAC;YACdC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,aAAa;YACpBL,OAAO,EAAE,GAAGU,YAAY,CAACE,UAAU,KAAKF,YAAY,CAACV,OAAO,EAAE;YAC9DQ,SAAS,EAAEE,YAAY,CAACF,SAAS;YACjCf,IAAI,EAAEiB;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA7B,SAAS,CAACQ,EAAE,CAAC,eAAe,EAAGwB,QAAQ,IAAK;QAC1C3C,eAAe,CAAC2C,QAAQ,CAAC;QACzBV,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,eAAe;UACtBL,OAAO,EAAE,GAAGa,QAAQ,CAACC,MAAM,CAACR,IAAI,iBAAiB;UACjDE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrBhB,IAAI,EAAEoB;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAhC,SAAS,CAACQ,EAAE,CAAC,eAAe,EAAGI,IAAI,IAAK;QACtCrB,aAAa,CAACsB,IAAI,IAAIA,IAAI,GAAG;UAAE,GAAGA,IAAI;UAAEK,MAAM,EAAE;QAAW,CAAC,GAAG,IAAI,CAAC;MACtE,CAAC,CAAC;MAEFlB,SAAS,CAACQ,EAAE,CAAC,eAAe,EAAGI,IAAI,IAAK;QACtCvB,eAAe,CAAC,IAAI,CAAC;QACrBE,aAAa,CAAC,IAAI,CAAC;QACnB+B,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,eAAe;UACtBL,OAAO,EAAE,wBAAwB;UACjCQ,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF5B,SAAS,CAACQ,EAAE,CAAC,YAAY,EAAGI,IAAI,IAAK;QACnCvB,eAAe,CAAC,IAAI,CAAC;QACrBE,aAAa,CAAC,IAAI,CAAC;QACnB+B,eAAe,CAAC;UACdC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,YAAY;UACnBL,OAAO,EAAE,gBAAgB;UACzBQ,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF5B,SAAS,CAACQ,EAAE,CAAC,aAAa,EAAGI,IAAI,IAAK;QACpCrB,aAAa,CAAC,IAAI,CAAC;QACnB+B,eAAe,CAAC;UACdC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,aAAa;UACpBL,OAAO,EAAEP,IAAI,CAACsB,MAAM,IAAI,6BAA6B;UACrDP,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA5B,SAAS,CAACQ,EAAE,CAAC,aAAa,EAAGI,IAAI,IAAK;QACpC;QACAH,OAAO,CAACC,GAAG,CAAC,GAAGE,IAAI,CAACuB,QAAQ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEFnC,SAAS,CAACQ,EAAE,CAAC,qBAAqB,EAAGI,IAAI,IAAK;QAC5C;QACAH,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC,CAAC,CAAC;;MAEF;MACAV,SAAS,CAACQ,EAAE,CAAC,sBAAsB,EAAGW,OAAO,IAAK;QAChDG,eAAe,CAAC;UACdC,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,mBAAmB;UAC1BL,OAAO,EAAE,sCAAsC;UAC/CQ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrBhB,IAAI,EAAEO;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpC,SAAS,CAACiB,SAAS,CAAC;MAEpB,OAAO,MAAM;QACXA,SAAS,CAACoC,KAAK,CAAC,CAAC;QACjBrD,SAAS,CAAC,IAAI,CAAC;QACfE,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;IACH;EACF,CAAC,EAAE,CAACa,eAAe,EAAED,KAAK,EAAED,IAAI,CAAC,CAAC;EAElC,MAAM0B,eAAe,GAAIO,YAAY,IAAK;IACxC,MAAMQ,EAAE,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAChC,MAAMC,eAAe,GAAG;MAAE,GAAGX,YAAY;MAAEQ;IAAG,CAAC;IAE/C1C,gBAAgB,CAACkB,IAAI,IAAI,CAAC2B,eAAe,EAAE,GAAG3B,IAAI,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElE;IACAC,UAAU,CAAC,MAAM;MACfC,kBAAkB,CAACN,EAAE,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMM,kBAAkB,GAAIN,EAAE,IAAK;IACjC1C,gBAAgB,CAACkB,IAAI,IAAIA,IAAI,CAACE,MAAM,CAAC6B,CAAC,IAAIA,CAAC,CAACP,EAAE,KAAKA,EAAE,CAAC,CAAC;EACzD,CAAC;EAED,MAAMQ,qBAAqB,GAAGA,CAAA,KAAM;IAClClD,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAMmD,QAAQ,GAAIC,UAAU,IAAK;IAC/B,IAAIjE,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,WAAW,EAAE;QAAED;MAAW,CAAC,CAAC;IAC1C;EACF,CAAC;EAED,MAAME,WAAW,GAAGA,CAACF,UAAU,EAAE5B,OAAO,EAAE+B,WAAW,GAAG,MAAM,EAAEC,aAAa,GAAG,IAAI,KAAK;IACvF,IAAIrE,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,cAAc,EAAE;QAC1BD,UAAU;QACV5B,OAAO;QACP+B,WAAW;QACXC;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAItB,QAAQ,IAAK;IACvC,IAAIhD,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,oBAAoB,EAAE;QAAElB;MAAS,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAACN,UAAU,EAAEO,QAAQ,EAAEH,aAAa,GAAG,IAAI,KAAK;IACnE,IAAIrE,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,eAAe,EAAE;QAC3BD,UAAU;QACVO,QAAQ;QACRH;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMI,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IACrC,IAAI3E,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,aAAa,EAAE;QAAEQ,MAAM;QAAEC;MAAO,CAAC,CAAC;MAC9ClE,aAAa,CAAC;QAAEiE,MAAM;QAAEC,MAAM;QAAEvC,MAAM,EAAE;MAAW,CAAC,CAAC;MACrD7B,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMqE,UAAU,GAAGA,CAACF,MAAM,EAAEC,MAAM,KAAK;IACrC,IAAI3E,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,aAAa,EAAE;QAAEQ,MAAM;QAAEC;MAAO,CAAC,CAAC;MAC9CpE,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMsE,OAAO,GAAGA,CAACH,MAAM,EAAEC,MAAM,KAAK;IAClC,IAAI3E,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,UAAU,EAAE;QAAEQ,MAAM;QAAEC;MAAO,CAAC,CAAC;MAC3ClE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMqE,WAAW,GAAIb,UAAU,IAAK;IAClC,IAAIjE,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,cAAc,EAAE;QAAED;MAAW,CAAC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMc,UAAU,GAAId,UAAU,IAAK;IACjC,IAAIjE,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,aAAa,EAAE;QAAED;MAAW,CAAC,CAAC;IAC5C;EACF,CAAC;EAED,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhF,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,eAAe,CAAC;IAC9B;EACF,CAAC;EAED,MAAMe,mBAAmB,GAAGA,CAAC5C,OAAO,EAAE6C,QAAQ,KAAK;IACjD,IAAIlF,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,kBAAkB,EAAE;QAAE7B,OAAO;QAAE6C;MAAS,CAAC,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACR,MAAM,EAAES,KAAK,KAAK;IACzC,IAAIpF,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,cAAc,EAAE;QAAES,MAAM;QAAES;MAAM,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACV,MAAM,EAAEW,MAAM,KAAK;IAC3C,IAAItF,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,eAAe,EAAE;QAAES,MAAM;QAAEW;MAAO,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACZ,MAAM,EAAEa,SAAS,KAAK;IAC9C,IAAIxF,MAAM,EAAE;MACVA,MAAM,CAACkE,IAAI,CAAC,sBAAsB,EAAE;QAAES,MAAM;QAAEa;MAAU,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMC,KAAK,GAAG;IACZzF,MAAM;IACNE,WAAW;IACXE,WAAW;IACXE,YAAY;IACZE,UAAU;IACVE,QAAQ;IACRE,aAAa;IAEb;IACA4B,eAAe;IACfqB,kBAAkB;IAClBE,qBAAqB;IAErB;IACAC,QAAQ;IACRG,WAAW;IACXG,kBAAkB;IAClBQ,WAAW;IACXC,UAAU;IAEV;IACAR,YAAY;IACZE,UAAU;IACVG,UAAU;IACVC,OAAO;IAEP;IACAG,YAAY;IACZC,mBAAmB;IAEnB;IACAE,eAAe;IACfE,gBAAgB;IAChBE,gBAAgB;IAEhB;IACA9E,aAAa;IACbF,eAAe;IACfI;EACF,CAAC;EAED,oBACEpB,OAAA,CAACC,aAAa,CAACkG,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3F,QAAA,EAClCA;EAAQ;IAAA6F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE7B,CAAC;AAAC/F,GAAA,CApUWF,cAAc;EAAA,QASgBR,OAAO;AAAA;AAAA0G,EAAA,GATrClG,cAAc;AAAA,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}