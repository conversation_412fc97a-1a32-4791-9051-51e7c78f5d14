{"name": "doctor-appointment-frontend", "version": "1.0.0", "description": "Frontend for Doctor Appointment Booking System", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "simple-peer": "^9.11.1", "react-icons": "^4.10.1"}, "devDependencies": {"tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}