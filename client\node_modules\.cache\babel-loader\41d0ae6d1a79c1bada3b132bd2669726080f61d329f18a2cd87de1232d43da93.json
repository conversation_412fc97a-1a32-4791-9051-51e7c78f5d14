{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\ProtectedRoute.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    isLoading\n  } = useAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check role if required\n  if (requiredRole && (user === null || user === void 0 ? void 0 : user.role) !== requiredRole) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"r/38E3umCJQz9nJgL/a0pLRGLAM=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "_s", "isAuthenticated", "user", "isLoading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "role", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/ProtectedRoute.jsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const { isAuthenticated, user, isLoading } = useAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Check role if required\n  if (requiredRole && user?.role !== requiredRole) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGT,OAAO,CAAC,CAAC;;EAEtD;EACA,IAAIS,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKQ,SAAS,EAAC,0DAA0D;MAAAN,QAAA,eACvEF,OAAA;QAAKQ,SAAS,EAAC;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;;EAEA;EACA,IAAI,CAACP,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACA,IAAIT,YAAY,IAAI,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,MAAKZ,YAAY,EAAE;IAC/C,oBAAOH,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACE,EAAA,CAvBIH,cAAc;EAAA,QAC2BH,OAAO;AAAA;AAAAkB,EAAA,GADhDf,cAAc;AAyBpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}