{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\ChatInterface.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { FiSend, FiVideo, FiPhone, FiPaperclip, FiSmile } from 'react-icons/fi';\nimport { useSocket } from '../context/SocketContext';\nimport { chatAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInterface = ({\n  receiverId,\n  receiverName,\n  receiverRole,\n  appointmentId = null,\n  onStartCall\n}) => {\n  _s();\n  var _receiverName$charAt;\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  const {\n    user\n  } = useAuth();\n  const {\n    socket,\n    joinChat,\n    sendMessage,\n    markMessagesAsRead,\n    startTyping,\n    stopTyping,\n    initiateCall\n  } = useSocket();\n\n  // Fetch messages on component mount\n  useEffect(() => {\n    fetchMessages();\n    if (receiverId) {\n      joinChat(receiverId);\n    }\n  }, [receiverId]);\n\n  // Listen for new messages\n  useEffect(() => {\n    if (!socket) return;\n    const handleNewMessage = message => {\n      if (message.sender._id === receiverId && message.receiver._id === user._id || message.sender._id === user._id && message.receiver._id === receiverId) {\n        setMessages(prev => [...prev, message]);\n\n        // Mark as read if message is from the other user\n        if (message.sender._id === receiverId) {\n          markMessagesAsRead(receiverId);\n        }\n      }\n    };\n    const handleUserTyping = data => {\n      if (data.userId === receiverId) {\n        setIsTyping(true);\n        // Clear typing after 3 seconds\n        setTimeout(() => setIsTyping(false), 3000);\n      }\n    };\n    const handleUserStoppedTyping = data => {\n      if (data.userId === receiverId) {\n        setIsTyping(false);\n      }\n    };\n    socket.on('new_message', handleNewMessage);\n    socket.on('user_typing', handleUserTyping);\n    socket.on('user_stopped_typing', handleUserStoppedTyping);\n    return () => {\n      socket.off('new_message', handleNewMessage);\n      socket.off('user_typing', handleUserTyping);\n      socket.off('user_stopped_typing', handleUserStoppedTyping);\n    };\n  }, [socket, receiverId, user._id, markMessagesAsRead]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const fetchMessages = async () => {\n    try {\n      setLoading(true);\n      const response = await chatAPI.getMessages(receiverId);\n      setMessages(response.data.data.messages);\n    } catch (error) {\n      setError('Failed to load messages');\n      console.error('Error fetching messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleSendMessage = e => {\n    e.preventDefault();\n    if (!newMessage.trim()) return;\n\n    // Send via socket for real-time delivery\n    sendMessage(receiverId, newMessage.trim(), 'text', appointmentId);\n\n    // Clear input\n    setNewMessage('');\n\n    // Stop typing indicator\n    stopTyping(receiverId);\n  };\n  const handleInputChange = e => {\n    setNewMessage(e.target.value);\n\n    // Handle typing indicator\n    if (e.target.value.trim()) {\n      startTyping(receiverId);\n\n      // Clear previous timeout\n      if (typingTimeoutRef.current) {\n        clearTimeout(typingTimeoutRef.current);\n      }\n\n      // Set new timeout to stop typing\n      typingTimeoutRef.current = setTimeout(() => {\n        stopTyping(receiverId);\n      }, 1000);\n    } else {\n      stopTyping(receiverId);\n    }\n  };\n  const handleStartCall = callType => {\n    if (onStartCall) {\n      onStartCall(receiverId, callType, appointmentId);\n    } else {\n      initiateCall(receiverId, callType, appointmentId);\n    }\n  };\n  const formatTime = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatDate = timestamp => {\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n\n  // Group messages by date\n  const groupedMessages = messages.reduce((groups, message) => {\n    const date = new Date(message.createdAt).toDateString();\n    if (!groups[date]) {\n      groups[date] = [];\n    }\n    groups[date].push(message);\n    return groups;\n  }, {});\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-primary-600 font-semibold\",\n            children: receiverName === null || receiverName === void 0 ? void 0 : (_receiverName$charAt = receiverName.charAt(0)) === null || _receiverName$charAt === void 0 ? void 0 : _receiverName$charAt.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-900\",\n            children: receiverName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 capitalize\",\n            children: receiverRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleStartCall('audio'),\n          className: \"p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors\",\n          title: \"Start audio call\",\n          children: /*#__PURE__*/_jsxDEV(FiPhone, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleStartCall('video'),\n          className: \"p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors\",\n          title: \"Start video call\",\n          children: /*#__PURE__*/_jsxDEV(FiVideo, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 text-sm\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), Object.entries(groupedMessages).map(([date, dateMessages]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center my-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-600\",\n            children: formatDate(dateMessages[0].createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), dateMessages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.sender._id === user._id ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.sender._id === user._id ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: message.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs mt-1 ${message.sender._id === user.id ? 'text-primary-100' : 'text-gray-500'}`,\n              children: formatTime(message.createdAt)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)\n        }, message._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this))]\n      }, date, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-start\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-100 px-4 py-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n              style: {\n                animationDelay: '0.1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n              style: {\n                animationDelay: '0.2s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSendMessage,\n      className: \"p-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(FiPaperclip, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newMessage,\n            onChange: handleInputChange,\n            placeholder: \"Type a message...\",\n            className: \"w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FiSmile, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: !newMessage.trim(),\n          className: \"p-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(FiSend, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"OstKPYivcZqpPkKmvuGSfT1o8tU=\", false, function () {\n  return [useAuth, useSocket];\n});\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FiSend", "FiVideo", "FiPhone", "FiPaperclip", "FiSmile", "useSocket", "chatAPI", "useAuth", "jsxDEV", "_jsxDEV", "ChatInterface", "receiverId", "<PERSON><PERSON><PERSON>", "receiverR<PERSON>", "appointmentId", "onStartCall", "_s", "_receiverName$charAt", "messages", "setMessages", "newMessage", "setNewMessage", "isTyping", "setIsTyping", "loading", "setLoading", "error", "setError", "messagesEndRef", "typingTimeoutRef", "user", "socket", "joinChat", "sendMessage", "markMessagesAsRead", "startTyping", "stopTyping", "initiateCall", "fetchMessages", "handleNewMessage", "message", "sender", "_id", "receiver", "prev", "handleUserTyping", "data", "userId", "setTimeout", "handleUserStoppedTyping", "on", "off", "scrollToBottom", "response", "getMessages", "console", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "e", "preventDefault", "trim", "handleInputChange", "target", "value", "clearTimeout", "handleStartCall", "callType", "formatTime", "timestamp", "Date", "toLocaleTimeString", "hour", "minute", "formatDate", "date", "today", "yesterday", "setDate", "getDate", "toDateString", "toLocaleDateString", "month", "day", "year", "getFullYear", "undefined", "groupedMessages", "reduce", "groups", "createdAt", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "onClick", "title", "size", "Object", "entries", "map", "dateMessages", "id", "style", "animationDelay", "ref", "onSubmit", "type", "onChange", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/ChatInterface.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { FiSend, FiVideo, FiPhone, FiPaperclip, FiSmile } from 'react-icons/fi';\nimport { useSocket } from '../context/SocketContext';\nimport { chatAPI } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\n\nconst ChatInterface = ({ receiverId, receiverName, receiverRole, appointmentId = null, onStartCall }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  \n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  \n  const { user } = useAuth();\n  const { \n    socket, \n    joinChat, \n    sendMessage, \n    markMessagesAsRead, \n    startTyping, \n    stopTyping,\n    initiateCall \n  } = useSocket();\n\n  // Fetch messages on component mount\n  useEffect(() => {\n    fetchMessages();\n    if (receiverId) {\n      joinChat(receiverId);\n    }\n  }, [receiverId]);\n\n  // Listen for new messages\n  useEffect(() => {\n    if (!socket) return;\n\n    const handleNewMessage = (message) => {\n      if (\n        (message.sender._id === receiverId && message.receiver._id === user._id) ||\n        (message.sender._id === user._id && message.receiver._id === receiverId)\n      ) {\n        setMessages(prev => [...prev, message]);\n        \n        // Mark as read if message is from the other user\n        if (message.sender._id === receiverId) {\n          markMessagesAsRead(receiverId);\n        }\n      }\n    };\n\n    const handleUserTyping = (data) => {\n      if (data.userId === receiverId) {\n        setIsTyping(true);\n        // Clear typing after 3 seconds\n        setTimeout(() => setIsTyping(false), 3000);\n      }\n    };\n\n    const handleUserStoppedTyping = (data) => {\n      if (data.userId === receiverId) {\n        setIsTyping(false);\n      }\n    };\n\n    socket.on('new_message', handleNewMessage);\n    socket.on('user_typing', handleUserTyping);\n    socket.on('user_stopped_typing', handleUserStoppedTyping);\n\n    return () => {\n      socket.off('new_message', handleNewMessage);\n      socket.off('user_typing', handleUserTyping);\n      socket.off('user_stopped_typing', handleUserStoppedTyping);\n    };\n  }, [socket, receiverId, user._id, markMessagesAsRead]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const fetchMessages = async () => {\n    try {\n      setLoading(true);\n      const response = await chatAPI.getMessages(receiverId);\n      setMessages(response.data.data.messages);\n    } catch (error) {\n      setError('Failed to load messages');\n      console.error('Error fetching messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    \n    if (!newMessage.trim()) return;\n\n    // Send via socket for real-time delivery\n    sendMessage(receiverId, newMessage.trim(), 'text', appointmentId);\n    \n    // Clear input\n    setNewMessage('');\n    \n    // Stop typing indicator\n    stopTyping(receiverId);\n  };\n\n  const handleInputChange = (e) => {\n    setNewMessage(e.target.value);\n    \n    // Handle typing indicator\n    if (e.target.value.trim()) {\n      startTyping(receiverId);\n      \n      // Clear previous timeout\n      if (typingTimeoutRef.current) {\n        clearTimeout(typingTimeoutRef.current);\n      }\n      \n      // Set new timeout to stop typing\n      typingTimeoutRef.current = setTimeout(() => {\n        stopTyping(receiverId);\n      }, 1000);\n    } else {\n      stopTyping(receiverId);\n    }\n  };\n\n  const handleStartCall = (callType) => {\n    if (onStartCall) {\n      onStartCall(receiverId, callType, appointmentId);\n    } else {\n      initiateCall(receiverId, callType, appointmentId);\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDate = (timestamp) => {\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n\n    if (date.toDateString() === today.toDateString()) {\n      return 'Today';\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday';\n    } else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n\n  // Group messages by date\n  const groupedMessages = messages.reduce((groups, message) => {\n    const date = new Date(message.createdAt).toDateString();\n    if (!groups[date]) {\n      groups[date] = [];\n    }\n    groups[date].push(message);\n    return groups;\n  }, {});\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-white\">\n      {/* Chat Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n            <span className=\"text-primary-600 font-semibold\">\n              {receiverName?.charAt(0)?.toUpperCase()}\n            </span>\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900\">{receiverName}</h3>\n            <p className=\"text-sm text-gray-500 capitalize\">{receiverRole}</p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => handleStartCall('audio')}\n            className=\"p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors\"\n            title=\"Start audio call\"\n          >\n            <FiPhone size={20} />\n          </button>\n          <button\n            onClick={() => handleStartCall('video')}\n            className=\"p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors\"\n            title=\"Start video call\"\n          >\n            <FiVideo size={20} />\n          </button>\n        </div>\n      </div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 text-sm\">\n            {error}\n          </div>\n        )}\n\n        {Object.entries(groupedMessages).map(([date, dateMessages]) => (\n          <div key={date}>\n            {/* Date Separator */}\n            <div className=\"flex items-center justify-center my-4\">\n              <div className=\"bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-600\">\n                {formatDate(dateMessages[0].createdAt)}\n              </div>\n            </div>\n\n            {/* Messages for this date */}\n            {dateMessages.map((message) => (\n              <div\n                key={message._id}\n                className={`flex ${message.sender._id === user._id ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                    message.sender._id === user._id\n                      ? 'bg-primary-600 text-white'\n                      : 'bg-gray-100 text-gray-900'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.message}</p>\n                  <p\n                    className={`text-xs mt-1 ${\n                      message.sender._id === user.id ? 'text-primary-100' : 'text-gray-500'\n                    }`}\n                  >\n                    {formatTime(message.createdAt)}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        ))}\n\n        {/* Typing Indicator */}\n        {isTyping && (\n          <div className=\"flex justify-start\">\n            <div className=\"bg-gray-100 px-4 py-2 rounded-lg\">\n              <div className=\"flex space-x-1\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <form onSubmit={handleSendMessage} className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <FiPaperclip size={20} />\n          </button>\n          \n          <div className=\"flex-1 relative\">\n            <input\n              type=\"text\"\n              value={newMessage}\n              onChange={handleInputChange}\n              placeholder=\"Type a message...\"\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n            <button\n              type=\"button\"\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <FiSmile size={18} />\n            </button>\n          </div>\n          \n          <button\n            type=\"submit\"\n            disabled={!newMessage.trim()}\n            className=\"p-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <FiSend size={18} />\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ChatInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,UAAU;EAAEC,YAAY;EAAEC,YAAY;EAAEC,aAAa,GAAG,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACvG,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,gBAAgB,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM;IAAE+B;EAAK,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAC1B,MAAM;IACJwB,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,kBAAkB;IAClBC,WAAW;IACXC,UAAU;IACVC;EACF,CAAC,GAAGhC,SAAS,CAAC,CAAC;;EAEf;EACAP,SAAS,CAAC,MAAM;IACdwC,aAAa,CAAC,CAAC;IACf,IAAI3B,UAAU,EAAE;MACdqB,QAAQ,CAACrB,UAAU,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACAb,SAAS,CAAC,MAAM;IACd,IAAI,CAACiC,MAAM,EAAE;IAEb,MAAMQ,gBAAgB,GAAIC,OAAO,IAAK;MACpC,IACGA,OAAO,CAACC,MAAM,CAACC,GAAG,KAAK/B,UAAU,IAAI6B,OAAO,CAACG,QAAQ,CAACD,GAAG,KAAKZ,IAAI,CAACY,GAAG,IACtEF,OAAO,CAACC,MAAM,CAACC,GAAG,KAAKZ,IAAI,CAACY,GAAG,IAAIF,OAAO,CAACG,QAAQ,CAACD,GAAG,KAAK/B,UAAW,EACxE;QACAQ,WAAW,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,OAAO,CAAC,CAAC;;QAEvC;QACA,IAAIA,OAAO,CAACC,MAAM,CAACC,GAAG,KAAK/B,UAAU,EAAE;UACrCuB,kBAAkB,CAACvB,UAAU,CAAC;QAChC;MACF;IACF,CAAC;IAED,MAAMkC,gBAAgB,GAAIC,IAAI,IAAK;MACjC,IAAIA,IAAI,CAACC,MAAM,KAAKpC,UAAU,EAAE;QAC9BY,WAAW,CAAC,IAAI,CAAC;QACjB;QACAyB,UAAU,CAAC,MAAMzB,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC5C;IACF,CAAC;IAED,MAAM0B,uBAAuB,GAAIH,IAAI,IAAK;MACxC,IAAIA,IAAI,CAACC,MAAM,KAAKpC,UAAU,EAAE;QAC9BY,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC;IAEDQ,MAAM,CAACmB,EAAE,CAAC,aAAa,EAAEX,gBAAgB,CAAC;IAC1CR,MAAM,CAACmB,EAAE,CAAC,aAAa,EAAEL,gBAAgB,CAAC;IAC1Cd,MAAM,CAACmB,EAAE,CAAC,qBAAqB,EAAED,uBAAuB,CAAC;IAEzD,OAAO,MAAM;MACXlB,MAAM,CAACoB,GAAG,CAAC,aAAa,EAAEZ,gBAAgB,CAAC;MAC3CR,MAAM,CAACoB,GAAG,CAAC,aAAa,EAAEN,gBAAgB,CAAC;MAC3Cd,MAAM,CAACoB,GAAG,CAAC,qBAAqB,EAAEF,uBAAuB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAAClB,MAAM,EAAEpB,UAAU,EAAEmB,IAAI,CAACY,GAAG,EAAER,kBAAkB,CAAC,CAAC;;EAEtD;EACApC,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClC,QAAQ,CAAC,CAAC;EAEd,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4B,QAAQ,GAAG,MAAM/C,OAAO,CAACgD,WAAW,CAAC3C,UAAU,CAAC;MACtDQ,WAAW,CAACkC,QAAQ,CAACP,IAAI,CAACA,IAAI,CAAC5B,QAAQ,CAAC;IAC1C,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,QAAQ,CAAC,yBAAyB,CAAC;MACnC4B,OAAO,CAAC7B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAI,qBAAA;IAC3B,CAAAA,qBAAA,GAAA5B,cAAc,CAAC6B,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC1C,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE;;IAExB;IACA9B,WAAW,CAACtB,UAAU,EAAES,UAAU,CAAC2C,IAAI,CAAC,CAAC,EAAE,MAAM,EAAEjD,aAAa,CAAC;;IAEjE;IACAO,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACAe,UAAU,CAACzB,UAAU,CAAC;EACxB,CAAC;EAED,MAAMqD,iBAAiB,GAAIH,CAAC,IAAK;IAC/BxC,aAAa,CAACwC,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC;;IAE7B;IACA,IAAIL,CAAC,CAACI,MAAM,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC,EAAE;MACzB5B,WAAW,CAACxB,UAAU,CAAC;;MAEvB;MACA,IAAIkB,gBAAgB,CAAC4B,OAAO,EAAE;QAC5BU,YAAY,CAACtC,gBAAgB,CAAC4B,OAAO,CAAC;MACxC;;MAEA;MACA5B,gBAAgB,CAAC4B,OAAO,GAAGT,UAAU,CAAC,MAAM;QAC1CZ,UAAU,CAACzB,UAAU,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLyB,UAAU,CAACzB,UAAU,CAAC;IACxB;EACF,CAAC;EAED,MAAMyD,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAItD,WAAW,EAAE;MACfA,WAAW,CAACJ,UAAU,EAAE0D,QAAQ,EAAEvD,aAAa,CAAC;IAClD,CAAC,MAAM;MACLuB,YAAY,CAAC1B,UAAU,EAAE0D,QAAQ,EAAEvD,aAAa,CAAC;IACnD;EACF,CAAC;EAED,MAAMwD,UAAU,GAAIC,SAAS,IAAK;IAChC,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIL,SAAS,IAAK;IAChC,MAAMM,IAAI,GAAG,IAAIL,IAAI,CAACD,SAAS,CAAC;IAChC,MAAMO,KAAK,GAAG,IAAIN,IAAI,CAAC,CAAC;IACxB,MAAMO,SAAS,GAAG,IAAIP,IAAI,CAACM,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIJ,IAAI,CAACK,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIL,IAAI,CAACK,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,WAAW;IACpB,CAAC,MAAM;MACL,OAAOL,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAET,IAAI,CAACU,WAAW,CAAC,CAAC,KAAKT,KAAK,CAACS,WAAW,CAAC,CAAC,GAAG,SAAS,GAAGC;MACjE,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGvE,QAAQ,CAACwE,MAAM,CAAC,CAACC,MAAM,EAAEnD,OAAO,KAAK;IAC3D,MAAMqC,IAAI,GAAG,IAAIL,IAAI,CAAChC,OAAO,CAACoD,SAAS,CAAC,CAACV,YAAY,CAAC,CAAC;IACvD,IAAI,CAACS,MAAM,CAACd,IAAI,CAAC,EAAE;MACjBc,MAAM,CAACd,IAAI,CAAC,GAAG,EAAE;IACnB;IACAc,MAAM,CAACd,IAAI,CAAC,CAACgB,IAAI,CAACrD,OAAO,CAAC;IAC1B,OAAOmD,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,IAAInE,OAAO,EAAE;IACX,oBACEf,OAAA;MAAKqF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDtF,OAAA;QAAKqF,SAAS,EAAC;MAAiE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EAEV;EAEA,oBACE1F,OAAA;IAAKqF,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5CtF,OAAA;MAAKqF,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBACxFtF,OAAA;QAAKqF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtF,OAAA;UAAKqF,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrFtF,OAAA;YAAMqF,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAC7CnF,YAAY,aAAZA,YAAY,wBAAAK,oBAAA,GAAZL,YAAY,CAAEwF,MAAM,CAAC,CAAC,CAAC,cAAAnF,oBAAA,uBAAvBA,oBAAA,CAAyBoF,WAAW,CAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN1F,OAAA;UAAAsF,QAAA,gBACEtF,OAAA;YAAIqF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAEnF;UAAY;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/D1F,OAAA;YAAGqF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAElF;UAAY;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1F,OAAA;QAAKqF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtF,OAAA;UACE6F,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,OAAO,CAAE;UACxC0B,SAAS,EAAC,6FAA6F;UACvGS,KAAK,EAAC,kBAAkB;UAAAR,QAAA,eAExBtF,OAAA,CAACP,OAAO;YAACsG,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACT1F,OAAA;UACE6F,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,OAAO,CAAE;UACxC0B,SAAS,EAAC,6FAA6F;UACvGS,KAAK,EAAC,kBAAkB;UAAAR,QAAA,eAExBtF,OAAA,CAACR,OAAO;YAACuG,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1F,OAAA;MAAKqF,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAClDrE,KAAK,iBACJjB,OAAA;QAAKqF,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFrE;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAM,MAAM,CAACC,OAAO,CAACjB,eAAe,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC9B,IAAI,EAAE+B,YAAY,CAAC,kBACxDnG,OAAA;QAAAsF,QAAA,gBAEEtF,OAAA;UAAKqF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDtF,OAAA;YAAKqF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACtEnB,UAAU,CAACgC,YAAY,CAAC,CAAC,CAAC,CAAChB,SAAS;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLS,YAAY,CAACD,GAAG,CAAEnE,OAAO,iBACxB/B,OAAA;UAEEqF,SAAS,EAAE,QAAQtD,OAAO,CAACC,MAAM,CAACC,GAAG,KAAKZ,IAAI,CAACY,GAAG,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAqD,QAAA,eAEvFtF,OAAA;YACEqF,SAAS,EAAE,6CACTtD,OAAO,CAACC,MAAM,CAACC,GAAG,KAAKZ,IAAI,CAACY,GAAG,GAC3B,2BAA2B,GAC3B,2BAA2B,EAC9B;YAAAqD,QAAA,gBAEHtF,OAAA;cAAGqF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEvD,OAAO,CAACA;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C1F,OAAA;cACEqF,SAAS,EAAE,gBACTtD,OAAO,CAACC,MAAM,CAACC,GAAG,KAAKZ,IAAI,CAAC+E,EAAE,GAAG,kBAAkB,GAAG,eAAe,EACpE;cAAAd,QAAA,EAEFzB,UAAU,CAAC9B,OAAO,CAACoD,SAAS;YAAC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAlBD3D,OAAO,CAACE,GAAG;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBb,CACN,CAAC;MAAA,GA/BMtB,IAAI;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCT,CACN,CAAC,EAGD7E,QAAQ,iBACPb,OAAA;QAAKqF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCtF,OAAA;UAAKqF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/CtF,OAAA;YAAKqF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BtF,OAAA;cAAKqF,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE1F,OAAA;cAAKqF,SAAS,EAAC,iDAAiD;cAACgB,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1G1F,OAAA;cAAKqF,SAAS,EAAC,iDAAiD;cAACgB,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED1F,OAAA;QAAKuG,GAAG,EAAEpF;MAAe;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGN1F,OAAA;MAAMwG,QAAQ,EAAErD,iBAAkB;MAACkC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eACzEtF,OAAA;QAAKqF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtF,OAAA;UACEyG,IAAI,EAAC,QAAQ;UACbpB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnEtF,OAAA,CAACN,WAAW;YAACqG,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAET1F,OAAA;UAAKqF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BtF,OAAA;YACEyG,IAAI,EAAC,MAAM;YACXhD,KAAK,EAAE9C,UAAW;YAClB+F,QAAQ,EAAEnD,iBAAkB;YAC5BoD,WAAW,EAAC,mBAAmB;YAC/BtB,SAAS,EAAC;UAAsI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjJ,CAAC,eACF1F,OAAA;YACEyG,IAAI,EAAC,QAAQ;YACbpB,SAAS,EAAC,yGAAyG;YAAAC,QAAA,eAEnHtF,OAAA,CAACL,OAAO;cAACoG,IAAI,EAAE;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1F,OAAA;UACEyG,IAAI,EAAC,QAAQ;UACbG,QAAQ,EAAE,CAACjG,UAAU,CAAC2C,IAAI,CAAC,CAAE;UAC7B+B,SAAS,EAAC,mIAAmI;UAAAC,QAAA,eAE7ItF,OAAA,CAACT,MAAM;YAACwG,IAAI,EAAE;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnF,EAAA,CAzTIN,aAAa;EAAA,QAUAH,OAAO,EASpBF,SAAS;AAAA;AAAAiH,EAAA,GAnBT5G,aAAa;AA2TnB,eAAeA,aAAa;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}