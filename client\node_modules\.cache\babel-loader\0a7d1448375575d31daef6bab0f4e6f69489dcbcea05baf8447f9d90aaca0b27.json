{"ast": null, "code": "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n  lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = arraybuffer => {\n  let bytes = new Uint8Array(arraybuffer),\n    i,\n    len = bytes.length,\n    base64 = '';\n  for (i = 0; i < len; i += 3) {\n    base64 += chars[bytes[i] >> 2];\n    base64 += chars[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n    base64 += chars[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n    base64 += chars[bytes[i + 2] & 63];\n  }\n  if (len % 3 === 2) {\n    base64 = base64.substring(0, base64.length - 1) + '=';\n  } else if (len % 3 === 1) {\n    base64 = base64.substring(0, base64.length - 2) + '==';\n  }\n  return base64;\n};\nexport const decode = base64 => {\n  let bufferLength = base64.length * 0.75,\n    len = base64.length,\n    i,\n    p = 0,\n    encoded1,\n    encoded2,\n    encoded3,\n    encoded4;\n  if (base64[base64.length - 1] === '=') {\n    bufferLength--;\n    if (base64[base64.length - 2] === '=') {\n      bufferLength--;\n    }\n  }\n  const arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n  for (i = 0; i < len; i += 4) {\n    encoded1 = lookup[base64.charCodeAt(i)];\n    encoded2 = lookup[base64.charCodeAt(i + 1)];\n    encoded3 = lookup[base64.charCodeAt(i + 2)];\n    encoded4 = lookup[base64.charCodeAt(i + 3)];\n    bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n    bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n    bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n  }\n  return arraybuffer;\n};", "map": {"version": 3, "names": ["chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "encode", "arraybuffer", "bytes", "len", "base64", "substring", "decode", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/03/client/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js"], "sourcesContent": ["// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n"], "mappings": "AAAA;AACA,MAAMA,KAAK,GAAG,kEAAkE;AAChF;AACA,MAAMC,MAAM,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAIA,UAAU,CAAC,GAAG,CAAC;AAC3E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;EACnCF,MAAM,CAACD,KAAK,CAACK,UAAU,CAACF,CAAC,CAAC,CAAC,GAAGA,CAAC;AACnC;AACA,OAAO,MAAMG,MAAM,GAAIC,WAAW,IAAK;EACnC,IAAIC,KAAK,GAAG,IAAIN,UAAU,CAACK,WAAW,CAAC;IAAEJ,CAAC;IAAEM,GAAG,GAAGD,KAAK,CAACJ,MAAM;IAAEM,MAAM,GAAG,EAAE;EAC3E,KAAKP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;IACzBO,MAAM,IAAIV,KAAK,CAACQ,KAAK,CAACL,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9BO,MAAM,IAAIV,KAAK,CAAE,CAACQ,KAAK,CAACL,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAKK,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,CAAC;IAC5DO,MAAM,IAAIV,KAAK,CAAE,CAACQ,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAKK,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,CAAC;IACjEO,MAAM,IAAIV,KAAK,CAACQ,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EACtC;EACA,IAAIM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACfC,MAAM,GAAGA,MAAM,CAACC,SAAS,CAAC,CAAC,EAAED,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;EACzD,CAAC,MACI,IAAIK,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACpBC,MAAM,GAAGA,MAAM,CAACC,SAAS,CAAC,CAAC,EAAED,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC1D;EACA,OAAOM,MAAM;AACjB,CAAC;AACD,OAAO,MAAME,MAAM,GAAIF,MAAM,IAAK;EAC9B,IAAIG,YAAY,GAAGH,MAAM,CAACN,MAAM,GAAG,IAAI;IAAEK,GAAG,GAAGC,MAAM,CAACN,MAAM;IAAED,CAAC;IAAEW,CAAC,GAAG,CAAC;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,QAAQ;EAC9G,IAAIR,MAAM,CAACA,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IACnCS,YAAY,EAAE;IACd,IAAIH,MAAM,CAACA,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACnCS,YAAY,EAAE;IAClB;EACJ;EACA,MAAMN,WAAW,GAAG,IAAIY,WAAW,CAACN,YAAY,CAAC;IAAEL,KAAK,GAAG,IAAIN,UAAU,CAACK,WAAW,CAAC;EACtF,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;IACzBY,QAAQ,GAAGd,MAAM,CAACS,MAAM,CAACL,UAAU,CAACF,CAAC,CAAC,CAAC;IACvCa,QAAQ,GAAGf,MAAM,CAACS,MAAM,CAACL,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3Cc,QAAQ,GAAGhB,MAAM,CAACS,MAAM,CAACL,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3Ce,QAAQ,GAAGjB,MAAM,CAACS,MAAM,CAACL,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3CK,KAAK,CAACM,CAAC,EAAE,CAAC,GAAIC,QAAQ,IAAI,CAAC,GAAKC,QAAQ,IAAI,CAAE;IAC9CR,KAAK,CAACM,CAAC,EAAE,CAAC,GAAI,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAKC,QAAQ,IAAI,CAAE;IACrDT,KAAK,CAACM,CAAC,EAAE,CAAC,GAAI,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAKC,QAAQ,GAAG,EAAG;EACxD;EACA,OAAOX,WAAW;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}