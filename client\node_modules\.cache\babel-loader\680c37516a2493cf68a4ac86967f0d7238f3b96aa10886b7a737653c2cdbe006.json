{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { SocketProvider } from './context/SocketContext';\n\n// Components\nimport Navbar from './components/Navbar';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport NotificationCenter from './components/NotificationCenter';\nimport CommunicationTest from './components/CommunicationTest';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport DoctorProfilePage from './pages/DoctorProfilePage';\nimport PatientDashboard from './pages/PatientDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(SocketProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationCenter, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 38\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/doctor/:id\",\n              element: /*#__PURE__*/_jsxDEV(DoctorProfilePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/test-communication\",\n              element: /*#__PURE__*/_jsxDEV(CommunicationTest, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 56\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/patient-dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredRole: \"patient\",\n                children: /*#__PURE__*/_jsxDEV(PatientDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/doctor-dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredRole: \"doctor\",\n                children: /*#__PURE__*/_jsxDEV(DoctorDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-6xl mb-4\",\n                    children: \"\\uD83D\\uDD0D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"Page Not Found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"The page you're looking for doesn't exist.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 62,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"/\",\n                    className: \"btn-primary\",\n                    children: \"Go Home\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "SocketProvider", "<PERSON><PERSON><PERSON>", "ProtectedRoute", "NotificationCenter", "CommunicationTest", "HomePage", "LoginPage", "RegisterPage", "DoctorProfilePage", "PatientDashboard", "DoctorDashboard", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "requiredRole", "href", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './context/AuthContext';\nimport { SocketProvider } from './context/SocketContext';\n\n// Components\nimport Navbar from './components/Navbar';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport NotificationCenter from './components/NotificationCenter';\nimport CommunicationTest from './components/CommunicationTest';\n\n// Pages\nimport HomePage from './pages/HomePage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport DoctorProfilePage from './pages/DoctorProfilePage';\nimport PatientDashboard from './pages/PatientDashboard';\nimport DoctorDashboard from './pages/DoctorDashboard';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <SocketProvider>\n        <Router>\n          <div className=\"App\">\n            <Navbar />\n            <NotificationCenter />\n            <Routes>\n            {/* Public Routes */}\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/register\" element={<RegisterPage />} />\n            <Route path=\"/doctor/:id\" element={<DoctorProfilePage />} />\n            <Route path=\"/test-communication\" element={<CommunicationTest />} />\n\n            {/* Protected Routes */}\n            <Route \n              path=\"/patient-dashboard\" \n              element={\n                <ProtectedRoute requiredRole=\"patient\">\n                  <PatientDashboard />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/doctor-dashboard\" \n              element={\n                <ProtectedRoute requiredRole=\"doctor\">\n                  <DoctorDashboard />\n                </ProtectedRoute>\n              } \n            />\n\n            {/* 404 Route */}\n            <Route \n              path=\"*\" \n              element={\n                <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-6xl mb-4\">🔍</div>\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Page Not Found</h2>\n                    <p className=\"text-gray-600 mb-4\">The page you're looking for doesn't exist.</p>\n                    <a href=\"/\" className=\"btn-primary\">\n                      Go Home\n                    </a>\n                  </div>\n                </div>\n              } \n            />\n          </Routes>\n        </div>\n      </Router>\n      </SocketProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,yBAAyB;;AAExD;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,iBAAiB,MAAM,gCAAgC;;AAE9D;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,YAAY;IAAAe,QAAA,eACXF,OAAA,CAACZ,cAAc;MAAAc,QAAA,eACbF,OAAA,CAAChB,MAAM;QAAAkB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBF,OAAA,CAACX,MAAM;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVP,OAAA,CAACT,kBAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtBP,OAAA,CAACf,MAAM;YAAAiB,QAAA,gBAEPF,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACP,QAAQ;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACN,SAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACL,YAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,aAAa;cAACC,OAAO,eAAET,OAAA,CAACJ,iBAAiB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,qBAAqB;cAACC,OAAO,eAAET,OAAA,CAACR,iBAAiB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGpEP,OAAA,CAACd,KAAK;cACJsB,IAAI,EAAC,oBAAoB;cACzBC,OAAO,eACLT,OAAA,CAACV,cAAc;gBAACoB,YAAY,EAAC,SAAS;gBAAAR,QAAA,eACpCF,OAAA,CAACH,gBAAgB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFP,OAAA,CAACd,KAAK;cACJsB,IAAI,EAAC,mBAAmB;cACxBC,OAAO,eACLT,OAAA,CAACV,cAAc;gBAACoB,YAAY,EAAC,QAAQ;gBAAAR,QAAA,eACnCF,OAAA,CAACF,eAAe;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFP,OAAA,CAACd,KAAK;cACJsB,IAAI,EAAC,GAAG;cACRC,OAAO,eACLT,OAAA;gBAAKG,SAAS,EAAC,0DAA0D;gBAAAD,QAAA,eACvEF,OAAA;kBAAKG,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBAC1BF,OAAA;oBAAKG,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCP,OAAA;oBAAIG,SAAS,EAAC,uCAAuC;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEP,OAAA;oBAAGG,SAAS,EAAC,oBAAoB;oBAAAD,QAAA,EAAC;kBAA0C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChFP,OAAA;oBAAGW,IAAI,EAAC,GAAG;oBAACR,SAAS,EAAC,aAAa;oBAAAD,QAAA,EAAC;kBAEpC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEnB;AAACK,EAAA,GAxDQX,GAAG;AA0DZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}